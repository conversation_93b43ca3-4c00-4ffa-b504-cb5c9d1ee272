<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== Twitter发布队列清理工具 ===\n";

global $wpdb;
$table_name = $wpdb->prefix . 'stp_twitter_posts';

// 检查数据库表是否存在
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
if (!$table_exists) {
    echo "❌ 数据库表不存在: $table_name\n";
    exit;
}

// 显示当前队列状态
echo "\n=== 当前队列状态 ===\n";
$pending_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'");
$failed_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'failed'");
$success_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'published'");

echo "✅ 成功发布: $success_count 条\n";
echo "⏳ 等待发布: $pending_count 条\n";
echo "❌ 发布失败: $failed_count 条\n";

// 显示失败的记录
if ($failed_count > 0) {
    echo "\n=== 失败记录详情 ===\n";
    $failed_records = $wpdb->get_results("
        SELECT id, post_id, error_message, created_at 
        FROM $table_name 
        WHERE status = 'failed' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    foreach ($failed_records as $record) {
        $post_title = get_the_title($record->post_id);
        echo "- ID: {$record->id}, 文章: {$post_title}, 时间: {$record->created_at}\n";
        echo "  错误: " . substr($record->error_message, 0, 100) . "...\n";
    }
}

// 清理操作
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_failed':
            $deleted = $wpdb->query("DELETE FROM $table_name WHERE status = 'failed'");
            echo "\n✅ 已删除 $deleted 条失败记录\n";
            break;
            
        case 'reset_failed_to_pending':
            $updated = $wpdb->query("UPDATE $table_name SET status = 'pending', error_message = NULL WHERE status = 'failed'");
            echo "\n✅ 已将 $updated 条失败记录重置为待发布\n";
            break;
            
        case 'clear_all_pending':
            $deleted = $wpdb->query("DELETE FROM $table_name WHERE status = 'pending'");
            echo "\n✅ 已删除 $deleted 条待发布记录\n";
            break;
            
        case 'clear_old_records':
            // 删除7天前的记录
            $deleted = $wpdb->query("DELETE FROM $table_name WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
            echo "\n✅ 已删除 $deleted 条7天前的记录\n";
            break;
    }
    
    echo "<script>setTimeout(function(){location.href='clear-twitter-queue.php';}, 2000);</script>\n";
    exit;
}

// 清理所有定时任务
if (isset($_GET['clear_crons'])) {
    wp_clear_scheduled_hook('stp_delayed_publish');
    wp_clear_scheduled_hook('stp_publish_to_twitter');
    echo "\n✅ 已清理所有Twitter定时任务\n";
    echo "<script>setTimeout(function(){location.href='clear-twitter-queue.php';}, 2000);</script>\n";
    exit;
}

echo "\n=== 清理操作 ===\n";
echo "<a href='?action=clear_failed'>删除所有失败记录</a><br>\n";
echo "<a href='?action=reset_failed_to_pending'>将失败记录重置为待发布</a><br>\n";
echo "<a href='?action=clear_all_pending'>删除所有待发布记录</a><br>\n";
echo "<a href='?action=clear_old_records'>删除7天前的记录</a><br>\n";
echo "<a href='?clear_crons=1'>清理所有定时任务</a><br>\n";

echo "\n=== 建议操作顺序 ===\n";
echo "1. 先清理所有定时任务\n";
echo "2. 删除所有失败记录（这些都是API限制导致的）\n";
echo "3. 重新授权Twitter API\n";
echo "4. 设置发布间隔为20分钟\n";
echo "5. 发布新文章测试\n";

echo "\n<a href='debug-twitter-schedule.php'>返回调试页面</a>\n";
?>
