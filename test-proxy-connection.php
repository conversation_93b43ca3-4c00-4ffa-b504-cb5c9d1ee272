<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== 代理连接测试工具 ===\n";

$proxy_host = get_option('stp_proxy_host', '************');
$proxy_port = get_option('stp_proxy_port', '7890');

echo "测试代理: $proxy_host:$proxy_port\n\n";

// 测试1: 基本连接测试
echo "=== 测试1: 基本代理连接 ===\n";
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'http://httpbin.org/ip',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
    CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
    CURLOPT_VERBOSE => false
));

$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($end_time - $start_time) * 1000, 2);

if ($error) {
    echo "❌ 代理连接失败: $error\n";
} else {
    echo "✅ 代理连接成功 (HTTP $http_code, 耗时: {$duration}ms)\n";
    if ($response) {
        $ip_info = json_decode($response, true);
        if (isset($ip_info['origin'])) {
            echo "代理IP: " . $ip_info['origin'] . "\n";
        }
    }
}

// 测试2: Twitter API连接测试
echo "\n=== 测试2: Twitter API连接测试 ===\n";
$endpoints = [
    'https://api.x.com/2/users/me',
    'https://api.twitter.com/2/users/me'
];

foreach ($endpoints as $endpoint) {
    echo "测试端点: $endpoint\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
        CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer test-token',
            'Content-Type: application/json'
        ),
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
    ));
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $duration = round(($end_time - $start_time) * 1000, 2);
    
    if ($error) {
        echo "❌ 连接失败: $error (耗时: {$duration}ms)\n";
    } else {
        echo "✅ 连接成功 (HTTP $http_code, 耗时: {$duration}ms)\n";
        if ($http_code == 401) {
            echo "⚠️ 认证失败（正常，因为使用测试令牌）\n";
        }
    }
    echo "\n";
}

// 测试3: 不使用代理的直连测试
echo "=== 测试3: 直连测试（不使用代理） ===\n";
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'https://api.x.com/2/users/me',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_HTTPHEADER => array(
        'Authorization: Bearer test-token',
        'Content-Type: application/json'
    ),
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false
));

$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($end_time - $start_time) * 1000, 2);

if ($error) {
    echo "❌ 直连失败: $error (耗时: {$duration}ms)\n";
    echo "✅ 确认需要使用代理访问Twitter\n";
} else {
    echo "✅ 直连成功 (HTTP $http_code, 耗时: {$duration}ms)\n";
    echo "⚠️ 可能不需要代理，或者代理配置有问题\n";
}

echo "\n=== 建议操作 ===\n";
echo "1. 如果代理连接经常超时，考虑更换代理服务器\n";
echo "2. 如果直连可用，可以尝试禁用代理\n";
echo "3. 检查AWS Korea服务器的网络状态\n";
echo "4. 重新授权Twitter API解决401错误\n";

echo "\n<a href='debug-twitter-schedule.php'>返回调试页面</a>\n";
?>
