<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== Twitter API认证修复工具 ===\n";

// 检查当前认证状态
$access_token = get_option('stp_access_token');
$refresh_token = get_option('stp_refresh_token');

echo "\n=== 当前认证状态 ===\n";
if ($access_token) {
    echo "✅ Access Token存在: " . substr($access_token, 0, 20) . "...\n";
} else {
    echo "❌ Access Token不存在\n";
}

if ($refresh_token) {
    echo "✅ Refresh Token存在: " . substr($refresh_token, 0, 20) . "...\n";
} else {
    echo "❌ Refresh Token不存在\n";
}

// 清理旧的认证信息
if (isset($_GET['clear_auth'])) {
    delete_option('stp_access_token');
    delete_option('stp_refresh_token');
    delete_option('stp_token_expires');
    echo "✅ 已清理所有认证信息\n";
    echo "<script>setTimeout(function(){location.href='fix-twitter-auth.php';}, 2000);</script>\n";
    exit;
}

// 测试代理连接
echo "\n=== 代理连接测试 ===\n";
$proxy_host = get_option('stp_proxy_host', '************');
$proxy_port = get_option('stp_proxy_port', '7890');

echo "代理服务器: $proxy_host:$proxy_port\n";

// 测试代理连接到Twitter
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'https://api.x.com/2/users/me',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
    CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
    CURLOPT_HTTPHEADER => array(
        'Authorization: Bearer ' . ($access_token ?: 'test-token'),
        'Content-Type: application/json'
    ),
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
    CURLOPT_VERBOSE => false
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 代理连接失败: $error\n";
    echo "建议检查代理服务器状态\n";
} else {
    echo "✅ 代理连接成功 (HTTP $http_code)\n";
    if ($http_code == 401) {
        echo "⚠️ 认证失败，需要重新授权\n";
    }
}

echo "\n=== 操作选项 ===\n";
echo "<a href='?clear_auth=1'>清理认证信息</a><br>\n";
echo "<a href='/wp-admin/admin.php?page=smart-twitter-publisher'>重新授权Twitter</a><br>\n";
echo "<a href='debug-twitter-schedule.php'>返回调试页面</a><br>\n";

// 显示重新授权步骤
echo "\n=== 重新授权步骤 ===\n";
echo "1. 点击上面的'清理认证信息'链接\n";
echo "2. 进入WordPress后台 -> Smart Twitter Publisher\n";
echo "3. 点击'获取授权URL'重新授权\n";
echo "4. 完成Twitter授权流程\n";
echo "5. 测试发布功能\n";

// 检查API密钥配置
echo "\n=== API配置检查 ===\n";
$client_id = get_option('stp_client_id');
$client_secret = get_option('stp_client_secret');

if ($client_id && $client_secret) {
    echo "✅ API密钥已配置\n";
    echo "Client ID: " . substr($client_id, 0, 10) . "...\n";
} else {
    echo "❌ API密钥未配置\n";
    echo "请在插件设置中配置Twitter API密钥\n";
}
?>
