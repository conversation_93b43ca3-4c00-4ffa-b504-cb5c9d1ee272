<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== Twitter API配置检查工具 ===\n";

// 检查API配置
$api_key = get_option('stp_api_key');
$api_secret = get_option('stp_api_secret');
$access_token = get_option('stp_access_token');
$refresh_token = get_option('stp_refresh_token');

echo "\n=== API配置状态 ===\n";
echo "API Key: " . ($api_key ? "✅ 已设置 (" . substr($api_key, 0, 10) . "...)" : "❌ 未设置") . "\n";
echo "API Secret: " . ($api_secret ? "✅ 已设置 (" . substr($api_secret, 0, 10) . "...)" : "❌ 未设置") . "\n";
echo "Access Token: " . ($access_token ? "✅ 已设置 (" . substr($access_token, 0, 20) . "...)" : "❌ 未设置") . "\n";
echo "Refresh Token: " . ($refresh_token ? "✅ 已设置 (" . substr($refresh_token, 0, 20) . "...)" : "❌ 未设置") . "\n";

if (!$access_token) {
    echo "\n❌ 没有Access Token，需要重新授权\n";
    echo "请进入WordPress后台 → Smart Twitter Publisher → 重新授权\n";
    exit;
}

// 测试Access Token有效性
echo "\n=== 测试Access Token有效性 ===\n";

$proxy_host = get_option('stp_proxy_host', '************');
$proxy_port = get_option('stp_proxy_port', '7890');

$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'https://api.x.com/2/users/me',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
    CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
    CURLOPT_HTTPHEADER => array(
        'Authorization: Bearer ' . $access_token,
        'Content-Type: application/json'
    ),
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 网络错误: $error\n";
} else {
    echo "HTTP状态码: $http_code\n";
    
    switch ($http_code) {
        case 200:
            echo "✅ Access Token有效！\n";
            $user_data = json_decode($response, true);
            if (isset($user_data['data']['username'])) {
                echo "Twitter用户名: @" . $user_data['data']['username'] . "\n";
            }
            break;
            
        case 401:
            echo "❌ Access Token已过期或无效\n";
            echo "需要重新授权获取新的Token\n";
            break;
            
        case 403:
            echo "⚠️ Token有效但权限不足\n";
            echo "可能需要申请更高级别的API权限\n";
            break;
            
        case 429:
            echo "⚠️ API调用频率超限\n";
            echo "请等待后重试\n";
            break;
            
        default:
            echo "❌ 未知错误 (HTTP $http_code)\n";
            if ($response) {
                $error_data = json_decode($response, true);
                if (isset($error_data['errors'])) {
                    foreach ($error_data['errors'] as $err) {
                        echo "错误详情: " . $err['message'] . "\n";
                    }
                }
            }
    }
}

// 检查API权限
echo "\n=== API权限检查 ===\n";
if ($http_code == 200 || $http_code == 403) {
    echo "尝试检查发推文权限...\n";
    
    $test_tweet = array(
        'text' => 'API权限测试 - 这条推文不会发布'
    );
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://api.x.com/2/tweets',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($test_tweet),
        CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json'
        ),
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
    ));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "发推文测试结果: HTTP $http_code\n";
    
    if ($http_code == 201) {
        echo "✅ 有发推文权限（但这是测试，推文未实际发布）\n";
    } elseif ($http_code == 403) {
        echo "❌ 没有发推文权限，需要申请Write权限\n";
    } elseif ($http_code == 401) {
        echo "❌ 认证失败，需要重新授权\n";
    }
}

echo "\n=== 解决建议 ===\n";
if ($http_code == 401) {
    echo "1. 重新授权Twitter API\n";
    echo "2. 或者重新申请API密钥\n";
} elseif ($http_code == 403) {
    echo "1. 检查Twitter Developer Portal中的应用权限\n";
    echo "2. 确保选择了'Read and Write'权限\n";
    echo "3. 可能需要升级到付费API计划\n";
} else {
    echo "1. 检查网络连接\n";
    echo "2. 检查代理设置\n";
}

echo "\n<a href='debug-twitter-schedule.php'>返回调试页面</a>\n";
?>
