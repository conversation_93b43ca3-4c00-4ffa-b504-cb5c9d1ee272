<?php
/**
 * Twitter 修复测试脚本
 * 
 * 用于测试Twitter发布修复是否有效
 */

// 加载WordPress环境
define('WP_USE_THEMES', false);

// 使用可靠的方法查找WordPress根目录
function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        if (file_exists($dir . '/wp-load.php')) {
            return $dir;
        }
    } while ($dir = realpath("$dir/.."));
    
    // 检查常见位置
    $possible_paths = [
        dirname(dirname(dirname(__DIR__))),
        dirname(dirname(__DIR__)),
        $_SERVER['DOCUMENT_ROOT'],
        '/var/www/html',
        '/mnt/sdc'
    ];
    
    foreach ($possible_paths as $path) {
        if (file_exists($path . '/wp-load.php')) {
            return $path;
        }
    }
    
    die('无法找到WordPress安装路径');
}

$wp_load_path = find_wordpress_base_path() . '/wp-load.php';
require_once($wp_load_path);

// 安全检查
if (!current_user_can('manage_options')) {
    die('权限不足');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Twitter 修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .button { padding: 8px 16px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Twitter 修复测试</h1>
    
    <div class="section">
        <h2>1. 检查修复状态</h2>
        <?php
        // 检查授权文件修复
        $auth_file = WP_CONTENT_DIR . '/../wp-content/plugins/twitter-auto-publish/admin/authorization.php';
        if (file_exists($auth_file)) {
            $auth_content = file_get_contents($auth_file);
            if (strpos($auth_content, 'is_wp_error($response)') !== false) {
                echo '<p class="success">✅ 授权文件已修复 - WP_Error处理已添加</p>';
            } else {
                echo '<p class="error">❌ 授权文件未修复 - 缺少WP_Error处理</p>';
            }
        } else {
            echo '<p class="error">❌ 找不到授权文件</p>';
        }
        
        // 检查代理助手修复
        $proxy_file = WP_CONTENT_DIR . '/plugins/twitter-proxy-helper/twitter-proxy-helper.php';
        if (file_exists($proxy_file)) {
            $proxy_content = file_get_contents($proxy_file);
            if (strpos($proxy_content, 'try {') !== false && strpos($proxy_content, 'setProxy($proxy_config)') !== false) {
                echo '<p class="success">✅ 代理助手已修复 - 添加了异常处理</p>';
            } else {
                echo '<p class="warning">⚠️ 代理助手可能需要进一步修复</p>';
            }
        } else {
            echo '<p class="error">❌ 找不到代理助手文件</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. 测试Twitter连接</h2>
        <?php
        if (isset($_POST['test_connection'])) {
            echo '<h3>正在测试Twitter API连接...</h3>';
            
            // 测试基本连接
            $ch = curl_init('https://api.twitter.com/2/openapi.json');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_PROXY, '13.209.96.45');
            curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
            curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // SOCKS5
            
            $start = microtime(true);
            $result = curl_exec($ch);
            $time = round((microtime(true) - $start) * 1000);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($error) {
                echo '<p class="error">连接失败: ' . $error . '</p>';
            } else {
                echo '<p class="success">连接成功! HTTP状态码: ' . $httpCode . ' (响应时间: ' . $time . 'ms)</p>';
            }
        }
        ?>
        <form method="post">
            <button type="submit" name="test_connection" class="button">测试Twitter API连接</button>
        </form>
    </div>
    
    <div class="section">
        <h2>3. 检查Twitter插件状态</h2>
        <?php
        // 检查Twitter插件是否激活
        if (is_plugin_active('twitter-auto-publish/twitter-auto-publish.php')) {
            echo '<p class="success">✅ Twitter Auto Publish插件已激活</p>';
            
            // 检查配置
            $client_id = get_option('xyz_twap_twconsumer_id');
            $client_secret = get_option('xyz_twap_twconsumer_secret');
            $access_token = get_option('xyz_twap_tw_token');
            
            if (!empty($client_id) && !empty($client_secret)) {
                echo '<p class="success">✅ Twitter API凭据已配置</p>';
            } else {
                echo '<p class="error">❌ Twitter API凭据未配置</p>';
            }
            
            if (!empty($access_token)) {
                echo '<p class="success">✅ Twitter访问令牌已获取</p>';
            } else {
                echo '<p class="error">❌ Twitter访问令牌未获取</p>';
            }
        } else {
            echo '<p class="error">❌ Twitter Auto Publish插件未激活</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>4. 最近的错误日志</h2>
        <?php
        $log_file = WP_CONTENT_DIR . '/debug.log';
        if (file_exists($log_file)) {
            $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if ($lines) {
                // 显示最后20行包含Twitter的日志
                $twitter_lines = array_filter($lines, function($line) {
                    return stripos($line, 'twitter') !== false || stripos($line, 'setProxy') !== false;
                });
                $recent_lines = array_slice($twitter_lines, -20);
                
                if ($recent_lines) {
                    echo '<h4>最近的Twitter相关日志:</h4>';
                    echo '<pre>' . implode("\n", $recent_lines) . '</pre>';
                } else {
                    echo '<p>没有找到最近的Twitter相关日志</p>';
                }
            }
        } else {
            echo '<p class="error">调试日志文件不存在</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>5. 建议的下一步</h2>
        <ul>
            <li>如果连接测试失败，请检查代理服务器是否正常工作</li>
            <li>如果插件配置不完整，请重新配置Twitter API凭据</li>
            <li>如果仍有错误，请查看完整的调试日志</li>
            <li>尝试发布一篇测试文章来验证修复效果</li>
        </ul>
        
        <p><a href="<?php echo admin_url('admin.php?page=twitter-auto-publish-settings'); ?>" class="button">前往Twitter插件设置</a></p>
        <p><a href="<?php echo plugin_dir_url(__FILE__) . 'test-publish.php'; ?>" class="button">测试发布功能</a></p>
    </div>
</body>
</html>
