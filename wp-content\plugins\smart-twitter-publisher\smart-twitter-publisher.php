<?php
/**
 * Plugin Name: Smart Twitter Publisher
 * Plugin URI: https://your-site.com
 * Description: 智能Twitter自动发布插件，专为中国服务器环境优化，支持SOCKS5代理
 * Version: 1.0.0
 * Author: Custom Development
 * License: GPL v2 or later
 * Text Domain: smart-twitter-publisher
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('STP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STP_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STP_VERSION', '1.0.0');

// 插件激活时的操作
register_activation_hook(__FILE__, 'stp_activate_plugin');
register_deactivation_hook(__FILE__, 'stp_deactivate_plugin');

function stp_activate_plugin() {
    // 创建必要的数据表
    stp_create_tables();
    
    // 设置默认选项
    if (!get_option('stp_proxy_host')) {
        update_option('stp_proxy_host', '************');
        update_option('stp_proxy_port', '7890');
        update_option('stp_ssl_verify', '0');
        update_option('stp_auto_publish', '1');
    }
}

function stp_deactivate_plugin() {
    // 清理定时任务
    wp_clear_scheduled_hook('stp_publish_to_twitter');
}

function stp_create_tables() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'stp_twitter_posts';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id bigint(20) NOT NULL,
        twitter_id varchar(100) DEFAULT '',
        status varchar(20) DEFAULT 'pending',
        message text,
        error_message text,
        retry_count int(3) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY post_id (post_id),
        KEY status (status)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

// 主插件类
class SmartTwitterPublisher {
    
    private $api_base = 'https://api.x.com';
    private $oauth_base = 'https://api.x.com/oauth2';
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_post_meta'));
        add_action('transition_post_status', array($this, 'handle_post_publish'), 10, 3);
        
        // AJAX处理
        add_action('wp_ajax_stp_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_stp_get_auth_url', array($this, 'ajax_get_auth_url'));
        add_action('wp_ajax_stp_handle_callback', array($this, 'ajax_handle_callback'));
        add_action('wp_ajax_stp_test_post_hook', array($this, 'ajax_test_post_hook'));
        add_action('wp_ajax_stp_revoke_auth', array($this, 'ajax_revoke_auth'));
        
        // 处理OAuth回调
        add_action('admin_init', array($this, 'handle_oauth_callback'));
        
        // 定时任务
        add_action('stp_publish_to_twitter', array($this, 'publish_to_twitter'));
        add_action('stp_delayed_publish', array($this, 'handle_delayed_publish'), 10, 3);
        
        // 添加管理页面样式
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
    }
    
    public function init() {
        // 初始化操作
    }
    
    public function admin_scripts($hook) {
        if (strpos($hook, 'smart-twitter-publisher') !== false) {
            wp_enqueue_script('jquery');
        }
    }
    
    public function admin_menu() {
        add_options_page(
            'Smart Twitter Publisher',
            'Twitter发布',
            'manage_options',
            'smart-twitter-publisher',
            array($this, 'admin_page')
        );
    }
    
    public function add_meta_boxes() {
        add_meta_box(
            'stp_twitter_box',
            'Twitter发布设置',
            array($this, 'meta_box_callback'),
            'post',
            'side',
            'high'
        );
    }
    
    public function meta_box_callback($post) {
        wp_nonce_field('stp_meta_box', 'stp_meta_box_nonce');
        
        $publish_to_twitter = get_post_meta($post->ID, '_stp_publish_to_twitter', true);
        $custom_message = get_post_meta($post->ID, '_stp_custom_message', true);
        $include_image = get_post_meta($post->ID, '_stp_include_image', true);
        
        ?>
        <div style="margin: 10px 0;">
            <label>
                <input type="checkbox" name="stp_publish_to_twitter" value="1" <?php checked($publish_to_twitter, '1'); ?>>
                <strong>发布到Twitter</strong>
            </label>
        </div>
        
        <div style="margin: 10px 0;">
            <label for="stp_custom_message"><strong>自定义消息:</strong></label>
            <textarea name="stp_custom_message" id="stp_custom_message" rows="3" style="width:100%; margin-top: 5px;" placeholder="留空则使用文章标题+链接"><?php echo esc_textarea($custom_message); ?></textarea>
            <small style="color: #666;">支持 {title} 和 {url} 占位符</small>
        </div>
        
        <div style="margin: 10px 0;">
            <label>
                <input type="checkbox" name="stp_include_image" value="1" <?php checked($include_image, '1'); ?>>
                包含特色图片
            </label>
        </div>
        
        <?php
        // 显示发布状态
        global $wpdb;
        $table_name = $wpdb->prefix . 'stp_twitter_posts';
        $twitter_post = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE post_id = %d ORDER BY created_at DESC LIMIT 1",
            $post->ID
        ));
        
        if ($twitter_post) {
            echo '<div style="margin: 10px 0; padding: 8px; background: #f0f0f1; border-radius: 3px;">';
            echo '<strong>Twitter状态:</strong> ';
            
            switch ($twitter_post->status) {
                case 'success':
                    echo '<span style="color: green;">✅ 已发布</span>';
                    if ($twitter_post->twitter_id) {
                        echo '<br><small>ID: ' . esc_html($twitter_post->twitter_id) . '</small>';
                    }
                    break;
                case 'failed':
                    echo '<span style="color: red;">❌ 发布失败</span>';
                    if ($twitter_post->error_message) {
                        echo '<br><small style="color: red;">' . esc_html($twitter_post->error_message) . '</small>';
                    }
                    break;
                case 'pending':
                    echo '<span style="color: orange;">⏳ 等待发布</span>';
                    break;
                default:
                    echo '<span style="color: gray;">未知状态</span>';
            }
            
            echo '<br><small>时间: ' . $twitter_post->created_at . '</small>';
            echo '</div>';
        }
        ?>
        <?php
    }
    
    public function save_post_meta($post_id) {
        if (!isset($_POST['stp_meta_box_nonce']) || !wp_verify_nonce($_POST['stp_meta_box_nonce'], 'stp_meta_box')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        $publish_to_twitter = isset($_POST['stp_publish_to_twitter']) ? '1' : '0';
        $custom_message = sanitize_textarea_field($_POST['stp_custom_message']);
        $include_image = isset($_POST['stp_include_image']) ? '1' : '0';
        
        update_post_meta($post_id, '_stp_publish_to_twitter', $publish_to_twitter);
        update_post_meta($post_id, '_stp_custom_message', $custom_message);
        update_post_meta($post_id, '_stp_include_image', $include_image);
    }
    
    public function handle_post_publish($new_status, $old_status, $post) {
        error_log('STP: handle_post_publish 被调用 - 文章ID: ' . $post->ID . ', 新状态: ' . $new_status . ', 旧状态: ' . $old_status . ', 类型: ' . $post->post_type);

        // 只处理文章发布
        if ($post->post_type !== 'post') {
            error_log('STP: 跳过非文章类型: ' . $post->post_type);
            return;
        }

        if ($new_status !== 'publish') {
            error_log('STP: 跳过非发布状态: ' . $new_status);
            return;
        }

        if ($old_status === 'publish') {
            error_log('STP: 跳过已发布文章的更新');
            return;
        }

        // 检查全局自动发布设置
        $auto_publish = get_option('stp_auto_publish', '0');
        error_log('STP: 全局自动发布设置: ' . $auto_publish);

        // 检查是否需要发布到Twitter
        $publish_to_twitter = get_post_meta($post->ID, '_stp_publish_to_twitter', true);
        error_log('STP: 文章Twitter发布设置: ' . $publish_to_twitter);

        // 如果全局开启自动发布，或者文章单独设置了发布
        if ($auto_publish !== '1' && $publish_to_twitter !== '1') {
            error_log('STP: 未启用Twitter发布，跳过');
            return;
        }

        // 检查是否已授权
        $access_token = get_option('stp_access_token');
        if (!$access_token) {
            error_log('STP: Twitter未授权，跳过发布');
            return;
        }

        error_log('STP: 准备发布到Twitter，文章ID: ' . $post->ID);

        // 延迟10秒发布，避免文章还未完全保存
        wp_schedule_single_event(time() + 10, 'stp_publish_to_twitter', array($post->ID));

        error_log('STP: 已安排Twitter发布任务，文章ID: ' . $post->ID);
    }
    
    public function admin_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }

        if (isset($_GET['revoke']) && $_GET['revoke'] === '1') {
            $this->revoke_authorization();
        }

        $client_id = get_option('stp_client_id', '');
        $client_secret = get_option('stp_client_secret', '');
        $access_token = get_option('stp_access_token', '');
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');
        $auto_publish = get_option('stp_auto_publish', '1');

        ?>
        <div class="wrap">
            <h1>Smart Twitter Publisher 设置</h1>

            <?php if (isset($_GET['cleared'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p>✅ 授权已撤销，错误状态已清除</p>
                </div>
            <?php endif; ?>

            <div class="notice notice-info">
                <p><strong>🚀 使用指南：</strong></p>
                <ol>
                    <li>在 <a href="https://developer.twitter.com/en/portal/dashboard" target="_blank">Twitter Developer Portal</a> 创建应用</li>
                    <li>获取 Client ID 和 Client Secret</li>
                    <li>设置回调URL为：<code><?php echo admin_url('options-general.php?page=smart-twitter-publisher&callback=1'); ?></code></li>
                    <li>填写下面的配置并保存</li>
                    <li>点击"开始授权"完成Twitter授权</li>
                </ol>
            </div>

            <form method="post" action="">
                <?php wp_nonce_field('stp_settings', 'stp_settings_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">Client ID</th>
                        <td>
                            <input type="text" name="stp_client_id" value="<?php echo esc_attr($client_id); ?>" class="regular-text" required />
                            <p class="description">从Twitter Developer Portal获取的Client ID</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Client Secret</th>
                        <td>
                            <input type="password" name="stp_client_secret" value="<?php echo esc_attr($client_secret); ?>" class="regular-text" required />
                            <p class="description">从Twitter Developer Portal获取的Client Secret</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">代理服务器</th>
                        <td>
                            <input type="text" name="stp_proxy_host" value="<?php echo esc_attr($proxy_host); ?>" class="regular-text" />
                            <p class="description">SOCKS5代理服务器地址（默认：************）</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">代理端口</th>
                        <td>
                            <input type="number" name="stp_proxy_port" value="<?php echo esc_attr($proxy_port); ?>" class="small-text" />
                            <p class="description">SOCKS5代理端口（默认：7890）</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">发布间隔</th>
                        <td>
                            <?php $publish_interval = get_option('stp_publish_interval', '5'); ?>
                            <select name="stp_publish_interval">
                                <option value="0" <?php selected($publish_interval, '0'); ?>>无间隔限制</option>
                                <option value="2" <?php selected($publish_interval, '2'); ?>>间隔2分钟</option>
                                <option value="5" <?php selected($publish_interval, '5'); ?>>间隔5分钟（推荐）</option>
                                <option value="10" <?php selected($publish_interval, '10'); ?>>间隔10分钟</option>
                                <option value="30" <?php selected($publish_interval, '30'); ?>>间隔30分钟</option>
                                <option value="60" <?php selected($publish_interval, '60'); ?>>间隔1小时</option>
                            </select>
                            <p class="description">设置文章之间的最小发布间隔（第一篇立即发布，后续文章检查间隔时间）</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">重复检测</th>
                        <td>
                            <?php $duplicate_check = get_option('stp_duplicate_check', '1'); ?>
                            <label>
                                <input type="checkbox" name="stp_duplicate_check" value="1" <?php checked($duplicate_check, '1'); ?> />
                                检测并避免发布重复或相似内容
                            </label>
                            <p class="description">防止因重复内容被Twitter标记为垃圾</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">自动发布</th>
                        <td>
                            <label>
                                <input type="checkbox" name="stp_auto_publish" value="1" <?php checked($auto_publish, '1'); ?> />
                                发布文章时自动同步到Twitter（如果已勾选）
                            </label>
                        </td>
                    </tr>
                </table>

                <?php submit_button('保存设置'); ?>
            </form>

            <hr>

            <h2>授权状态</h2>
            <?php if ($access_token): ?>
                <div class="notice notice-success">
                    <p>✅ <strong>Twitter授权已完成</strong></p>
                    <p>访问令牌：<code><?php echo substr($access_token, 0, 20) . '...'; ?></code></p>
                </div>

                <p>
                    <button type="button" class="button" onclick="testConnection()">🔍 测试连接</button>
                    <a href="<?php echo admin_url('options-general.php?page=smart-twitter-publisher&revoke=1'); ?>"
                       class="button button-secondary"
                       onclick="return confirm('确定要撤销Twitter授权吗？')">🗑️ 撤销授权</a>
                </p>

            <?php else: ?>
                <div class="notice notice-warning">
                    <p>⚠️ <strong>尚未完成Twitter授权</strong></p>
                </div>

                <?php if ($client_id && $client_secret): ?>
                    <p>
                        <button type="button" class="button button-primary" onclick="startAuthorization()">🚀 开始授权</button>
                        <button type="button" class="button" onclick="testConnection()">🔍 测试代理连接</button>
                    </p>
                <?php else: ?>
                    <p class="description">请先填写并保存Client ID和Client Secret</p>
                <?php endif; ?>
            <?php endif; ?>

            <div id="test-result" style="margin-top: 15px;"></div>

            <hr>

            <h2>⏰ 发布间隔说明</h2>
            <div class="notice notice-info">
                <p><strong>智能发布逻辑：</strong></p>
                <ul>
                    <li>✅ <strong>第一篇文章</strong>：立即发布到Twitter</li>
                    <li>⏰ <strong>后续文章</strong>：检查与上次发布的时间间隔</li>
                    <li>🚀 <strong>间隔足够</strong>：立即发布</li>
                    <li>⏳ <strong>间隔不足</strong>：自动延迟到满足间隔要求</li>
                </ul>
                <p><strong>示例：</strong>设置间隔5分钟，第一篇文章10:00发布，第二篇文章10:03发布，系统会延迟到10:05才发布第二篇。</p>
                <p><strong>优势：</strong>避免频繁发布被Twitter标记为垃圾，同时保证第一篇文章能立即同步。</p>
            </div>

            <h2>📊 发布历史</h2>
            <?php $this->show_publish_history(); ?>

            <h2>🧪 插件测试</h2>
            <div class="notice notice-info">
                <p><strong>测试说明：</strong>点击下面的按钮测试插件各项功能是否正常。</p>
            </div>
            <p>
                <button type="button" class="button" onclick="testPluginStatus()">🔍 测试插件状态</button>
                <button type="button" class="button" onclick="testPostHook()">📝 测试文章发布钩子</button>
            </p>
            <div id="plugin-test-result" style="margin-top: 15px;"></div>

            <h2>📝 系统日志</h2>
            <div class="notice notice-info">
                <p><strong>日志说明：</strong>显示最近的Twitter发布日志，包括成功、失败和延迟发布的记录。</p>
            </div>
            <?php $this->show_system_logs(); ?>
        </div>

        <script>
        // 确保ajaxurl可用
        if (typeof ajaxurl === 'undefined') {
            var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
        }

        function testConnection() {
            const resultDiv = document.getElementById('test-result');
            if (!resultDiv) {
                alert('页面元素未找到，请刷新页面');
                return;
            }

            resultDiv.innerHTML = '<div class="notice notice-info"><p>🔄 正在测试连接...</p></div>';

            const formData = new FormData();
            formData.append('action', 'stp_test_connection');
            formData.append('nonce', '<?php echo wp_create_nonce('stp_test'); ?>');

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + data.data + '</p></div>';
                } else {
                    resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ ' + (data.data || '测试失败') + '</p></div>';
                }
            })
            .catch(error => {
                console.error('测试连接错误:', error);
                resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ 连接测试失败: ' + error.message + '</p></div>';
            });
        }

        function testPluginStatus() {
            const resultDiv = document.getElementById('plugin-test-result');
            if (!resultDiv) {
                alert('页面元素未找到');
                return;
            }

            resultDiv.innerHTML = '<div class="notice notice-info"><p>🔄 正在检测插件状态...</p></div>';

            // 检测各项设置
            let status = '<div class="notice notice-info"><h4>📊 插件状态检测结果：</h4><ul>';

            // 检查设置项
            const autoPublish = document.querySelector('input[name="stp_auto_publish"]');
            status += '<li>🤖 自动发布设置: ' + (autoPublish && autoPublish.checked ? '✅ 已启用' : '❌ 未启用') + '</li>';

            const clientId = document.querySelector('input[name="stp_client_id"]');
            status += '<li>🔑 Client ID: ' + (clientId && clientId.value ? '✅ 已设置' : '❌ 未设置') + '</li>';

            const clientSecret = document.querySelector('input[name="stp_client_secret"]');
            status += '<li>🔐 Client Secret: ' + (clientSecret && clientSecret.value ? '✅ 已设置' : '❌ 未设置') + '</li>';

            const proxyHost = document.querySelector('input[name="stp_proxy_host"]');
            status += '<li>🌐 代理服务器: ' + (proxyHost && proxyHost.value ? '✅ ' + proxyHost.value : '❌ 未设置') + '</li>';

            status += '</ul></div>';

            resultDiv.innerHTML = status;
        }

        function testPostHook() {
            const resultDiv = document.getElementById('plugin-test-result');
            if (!resultDiv) {
                alert('页面元素未找到');
                return;
            }

            resultDiv.innerHTML = '<div class="notice notice-info"><p>🔄 正在测试文章发布钩子...</p></div>';

            const formData = new FormData();
            formData.append('action', 'stp_test_post_hook');
            formData.append('nonce', '<?php echo wp_create_nonce('stp_test'); ?>');

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + data.data + '</p></div>';
                } else {
                    resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ ' + (data.data || '测试失败') + '</p></div>';
                }
            })
            .catch(error => {
                console.error('测试钩子错误:', error);
                resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ 钩子测试失败: ' + error.message + '</p></div>';
            });
        }

        function startAuthorization() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="notice notice-info"><p>🔄 正在获取授权URL...</p></div>';

            fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=stp_get_auth_url&nonce=<?php echo wp_create_nonce('stp_auth'); ?>'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>✅ 正在跳转到Twitter授权页面...</p></div>';
                    window.location.href = data.data;
                } else {
                    resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ ' + data.data + '</p></div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="notice notice-error"><p>❌ 获取授权URL失败: ' + error.message + '</p></div>';
            });
        }
        </script>
        <?php
    }

    private function show_publish_history() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'stp_twitter_posts';

        $results = $wpdb->get_results(
            "SELECT p.*, posts.post_title
             FROM $table_name p
             LEFT JOIN {$wpdb->posts} posts ON p.post_id = posts.ID
             ORDER BY p.created_at DESC
             LIMIT 10"
        );

        if ($results) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>文章标题</th><th>发布状态</th><th>Twitter链接</th><th>发布内容</th><th>创建时间</th><th>更新时间</th><th>错误信息</th></tr></thead>';
            echo '<tbody>';

            foreach ($results as $row) {
                echo '<tr>';

                // 文章标题
                echo '<td>';
                if ($row->post_title) {
                    echo '<strong>' . esc_html($row->post_title) . '</strong><br>';
                    echo '<small>ID: ' . $row->post_id . '</small>';
                } else {
                    echo '<span style="color: #999;">未知文章 (ID: ' . $row->post_id . ')</span>';
                }
                echo '</td>';

                // 发布状态
                echo '<td>';
                switch ($row->status) {
                    case 'success':
                        echo '<span style="color: green; font-weight: bold;">✅ 发布成功</span>';
                        break;
                    case 'failed':
                        echo '<span style="color: red; font-weight: bold;">❌ 发布失败</span>';
                        if ($row->retry_count > 0) {
                            echo '<br><small>重试次数: ' . $row->retry_count . '</small>';
                        }
                        break;
                    case 'pending':
                        echo '<span style="color: orange; font-weight: bold;">⏳ 等待发布</span>';
                        break;
                    default:
                        echo '<span style="color: gray;">' . esc_html($row->status) . '</span>';
                }
                echo '</td>';

                // Twitter链接
                echo '<td>';
                if ($row->twitter_id) {
                    echo '<a href="https://twitter.com/i/web/status/' . esc_attr($row->twitter_id) . '" target="_blank" style="color: #1da1f2;">';
                    echo '🐦 查看推文</a><br>';
                    echo '<small>ID: ' . esc_html($row->twitter_id) . '</small>';
                } else {
                    echo '<span style="color: #999;">-</span>';
                }
                echo '</td>';

                // 发布内容
                echo '<td>';
                if ($row->message) {
                    $message = esc_html($row->message);
                    if (mb_strlen($message) > 50) {
                        echo '<span title="' . $message . '">' . mb_substr($message, 0, 50) . '...</span>';
                    } else {
                        echo $message;
                    }
                } else {
                    echo '<span style="color: #999;">-</span>';
                }
                echo '</td>';

                // 创建时间
                echo '<td>';
                echo '<strong>' . esc_html($row->created_at) . '</strong>';
                echo '</td>';

                // 更新时间
                echo '<td>';
                if ($row->updated_at && $row->updated_at !== $row->created_at) {
                    echo esc_html($row->updated_at);
                } else {
                    echo '<span style="color: #999;">-</span>';
                }
                echo '</td>';

                // 错误信息
                echo '<td>';
                if ($row->error_message) {
                    echo '<span style="color: red; font-size: 12px;">' . esc_html($row->error_message) . '</span>';
                } else {
                    echo '<span style="color: #999;">-</span>';
                }
                echo '</td>';

                echo '</tr>';
            }

            echo '</tbody></table>';

            // 添加统计信息
            $stats = $wpdb->get_row(
                "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
                 FROM $table_name"
            );

            if ($stats) {
                echo '<div style="margin-top: 15px; padding: 10px; background: #f9f9f9; border-radius: 5px;">';
                echo '<h4>📈 发布统计</h4>';
                echo '<p>';
                echo '<strong>总计：</strong>' . $stats->total . ' 条 | ';
                echo '<span style="color: green;"><strong>成功：</strong>' . $stats->success . ' 条</span> | ';
                echo '<span style="color: red;"><strong>失败：</strong>' . $stats->failed . ' 条</span> | ';
                echo '<span style="color: orange;"><strong>等待：</strong>' . $stats->pending . ' 条</span>';
                echo '</p>';
                echo '</div>';
            }
        } else {
            echo '<div class="notice notice-info"><p>📝 暂无发布记录。发布文章后，这里会显示Twitter同步状态。</p></div>';
        }
    }

    public function save_settings() {
        if (!isset($_POST['stp_settings_nonce']) || !wp_verify_nonce($_POST['stp_settings_nonce'], 'stp_settings')) {
            wp_die('安全验证失败');
        }

        update_option('stp_client_id', sanitize_text_field($_POST['stp_client_id']));
        update_option('stp_client_secret', sanitize_text_field($_POST['stp_client_secret']));
        update_option('stp_proxy_host', sanitize_text_field($_POST['stp_proxy_host']));
        update_option('stp_proxy_port', intval($_POST['stp_proxy_port']));
        update_option('stp_publish_interval', intval($_POST['stp_publish_interval']));
        update_option('stp_duplicate_check', isset($_POST['stp_duplicate_check']) ? '1' : '0');
        update_option('stp_auto_publish', isset($_POST['stp_auto_publish']) ? '1' : '0');

        echo '<div class="notice notice-success is-dismissible"><p>✅ 设置已保存</p></div>';
    }

    public function revoke_authorization() {
        delete_option('stp_access_token');
        delete_option('stp_refresh_token');
        delete_option('stp_token_expires');
        delete_option('stp_auth_state');
        delete_option('stp_code_verifier');

        wp_redirect(admin_url('options-general.php?page=smart-twitter-publisher&cleared=1'));
        exit;
    }

    public function ajax_test_connection() {
        check_ajax_referer('stp_test', 'nonce');

        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        // 首先测试不使用代理的连接
        $test_results = array();

        // 使用cURL直接测试SOCKS5代理
        $curl_result = $this->test_curl_socks5_proxy($proxy_host, $proxy_port);
        $test_results[] = $curl_result;

        // 测试1: 直接连接（不使用代理）
        $args_direct = array(
            'timeout' => 10,
            'sslverify' => false,
            'headers' => array(
                'User-Agent' => 'Smart Twitter Publisher/1.0'
            )
        );

        $response_direct = wp_remote_get('https://httpbin.org/ip', $args_direct);

        if (is_wp_error($response_direct)) {
            $test_results[] = '❌ 直接连接失败: ' . $response_direct->get_error_message();
        } else {
            $code = wp_remote_retrieve_response_code($response_direct);
            if ($code === 200) {
                $body = wp_remote_retrieve_body($response_direct);
                $data = json_decode($body, true);
                if (isset($data['origin'])) {
                    $test_results[] = '✅ 直接连接成功，IP: ' . $data['origin'];
                } else {
                    $test_results[] = '✅ 直接连接成功，HTTP: ' . $code;
                }
            } else {
                $test_results[] = '⚠️ 直接连接HTTP错误: ' . $code;
            }
        }

        // 测试2: 使用代理连接
        $args_proxy = array(
            'timeout' => 15,
            'sslverify' => false,
            'proxy' => array(
                'http' => $proxy_host . ':' . $proxy_port,
                'https' => $proxy_host . ':' . $proxy_port,
            ),
            'headers' => array(
                'User-Agent' => 'Smart Twitter Publisher/1.0'
            )
        );

        $response_proxy = wp_remote_get('https://httpbin.org/ip', $args_proxy);

        if (is_wp_error($response_proxy)) {
            $test_results[] = '❌ 代理连接失败: ' . $response_proxy->get_error_message();
        } else {
            $code = wp_remote_retrieve_response_code($response_proxy);
            if ($code === 200) {
                $body = wp_remote_retrieve_body($response_proxy);
                $data = json_decode($body, true);
                if (isset($data['origin'])) {
                    $test_results[] = '✅ 代理连接成功，IP: ' . $data['origin'];
                } else {
                    $test_results[] = '✅ 代理连接成功，HTTP: ' . $code;
                }
            } else {
                $test_results[] = '⚠️ 代理连接HTTP错误: ' . $code;
            }
        }

        // 测试3: 使用SOCKS5代理格式
        $args_socks5 = array(
            'timeout' => 15,
            'sslverify' => false,
            'proxy' => 'socks5://' . $proxy_host . ':' . $proxy_port,
            'headers' => array(
                'User-Agent' => 'Smart Twitter Publisher/1.0'
            )
        );

        $response_socks5 = wp_remote_get('https://httpbin.org/ip', $args_socks5);

        if (is_wp_error($response_socks5)) {
            $test_results[] = '❌ SOCKS5代理连接失败: ' . $response_socks5->get_error_message();
        } else {
            $code = wp_remote_retrieve_response_code($response_socks5);
            if ($code === 200) {
                $body = wp_remote_retrieve_body($response_socks5);
                $data = json_decode($body, true);
                if (isset($data['origin'])) {
                    $test_results[] = '✅ SOCKS5代理连接成功，IP: ' . $data['origin'];
                } else {
                    $test_results[] = '✅ SOCKS5代理连接成功，HTTP: ' . $code;
                }
            } else {
                $test_results[] = '⚠️ SOCKS5代理HTTP错误: ' . $code;
            }
        }

        // 测试4: 使用cURL测试Twitter API连接（多种方式）
        $twitter_endpoints = array(
            'api.x.com' => 'https://api.x.com/2/tweets/search/recent?query=hello&max_results=10',
            'api.twitter.com' => 'https://api.twitter.com/2/tweets/search/recent?query=hello&max_results=10'
        );

        $twitter_success = false;
        foreach ($twitter_endpoints as $endpoint_name => $endpoint_url) {
            $twitter_tests = array(
                'Auto' => CURL_SSLVERSION_DEFAULT,
                'TLS 1.2' => CURL_SSLVERSION_TLSv1_2,
                'TLS 1.0' => CURL_SSLVERSION_TLSv1_0
            );

            foreach ($twitter_tests as $ssl_name => $ssl_version) {
                $twitter_result = $this->test_twitter_api_endpoint($endpoint_url, $ssl_version);

                if ($twitter_result['success'] || $twitter_result['http_code'] === 401) {
                    $test_results[] = '✅ Twitter API可达 (' . $endpoint_name . ', ' . $ssl_name . ', HTTP ' . $twitter_result['http_code'] . ')';
                    $twitter_success = true;
                    break 2; // 跳出两层循环
                } else {
                    $test_results[] = '⚠️ Twitter API (' . $endpoint_name . ', ' . $ssl_name . '): ' . $twitter_result['message'];
                }
            }
        }

        if (!$twitter_success) {
            $test_results[] = '❌ 所有Twitter API连接尝试均失败';
            // 尝试通过代理访问普通HTTPS网站测试
            $https_test = $this->test_https_through_proxy();
            $test_results[] = $https_test;
        }

        // 返回所有测试结果
        $result_message = implode('<br>', $test_results);

        // 判断整体状态
        $has_success = false;
        foreach ($test_results as $result) {
            if (strpos($result, '✅') !== false) {
                $has_success = true;
                break;
            }
        }

        if ($has_success) {
            wp_send_json_success($result_message);
        } else {
            wp_send_json_error($result_message);
        }
    }

    public function ajax_test_post_hook() {
        if (!wp_verify_nonce($_POST['nonce'], 'stp_test')) {
            wp_send_json_error('安全验证失败');
            return;
        }

        // 检查钩子是否正确注册
        global $wp_filter;

        $results = array();

        // 检查transition_post_status钩子
        if (isset($wp_filter['transition_post_status'])) {
            $found = false;
            foreach ($wp_filter['transition_post_status']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) &&
                        $callback['function'][1] === 'handle_post_publish') {
                        $found = true;
                        $results[] = "✅ transition_post_status 钩子已注册 (优先级: $priority)";
                        break 2;
                    }
                }
            }
            if (!$found) {
                $results[] = "❌ transition_post_status 钩子未找到";
            }
        } else {
            $results[] = "❌ transition_post_status 钩子不存在";
        }

        // 检查定时任务钩子
        if (isset($wp_filter['stp_publish_to_twitter'])) {
            $results[] = "✅ stp_publish_to_twitter 钩子已注册";
        } else {
            $results[] = "❌ stp_publish_to_twitter 钩子未注册";
        }

        if (isset($wp_filter['stp_delayed_publish'])) {
            $results[] = "✅ stp_delayed_publish 钩子已注册";
        } else {
            $results[] = "❌ stp_delayed_publish 钩子未注册";
        }

        // 检查设置
        $auto_publish = get_option('stp_auto_publish', '0');
        $results[] = "🤖 自动发布设置: " . ($auto_publish === '1' ? '✅ 已启用' : '❌ 未启用');

        $access_token = get_option('stp_access_token');
        $results[] = "🔑 访问令牌: " . ($access_token ? '✅ 已设置' : '❌ 未设置');

        // 模拟触发一次日志记录
        error_log('STP: 测试钩子功能 - ' . date('Y-m-d H:i:s'));
        $results[] = "📝 已写入测试日志";

        wp_send_json_success('<h4>🧪 钩子测试结果：</h4><ul><li>' . implode('</li><li>', $results) . '</li></ul>');
    }

    public function ajax_get_auth_url() {
        check_ajax_referer('stp_auth', 'nonce');

        $client_id = get_option('stp_client_id');
        if (!$client_id) {
            wp_send_json_error('请先配置Client ID');
        }

        // 生成PKCE参数
        $code_verifier = $this->generate_code_verifier();
        $code_challenge = $this->generate_code_challenge($code_verifier);
        $state = wp_generate_password(32, false);

        // 保存状态
        update_option('stp_auth_state', $state);
        update_option('stp_code_verifier', $code_verifier);

        $callback_url = admin_url('options-general.php?page=smart-twitter-publisher&callback=1');

        $auth_url = 'https://twitter.com/i/oauth2/authorize?' . http_build_query(array(
            'response_type' => 'code',
            'client_id' => $client_id,
            'redirect_uri' => $callback_url,
            'scope' => 'tweet.read tweet.write users.read offline.access',
            'state' => $state,
            'code_challenge' => $code_challenge,
            'code_challenge_method' => 'S256'
        ));

        wp_send_json_success($auth_url);
    }

    public function handle_oauth_callback() {
        if (!isset($_GET['page']) || $_GET['page'] !== 'smart-twitter-publisher') {
            return;
        }

        if (!isset($_GET['callback']) || $_GET['callback'] !== '1') {
            return;
        }

        if (isset($_GET['error'])) {
            $error = sanitize_text_field($_GET['error']);
            $error_description = isset($_GET['error_description']) ? sanitize_text_field($_GET['error_description']) : '';

            echo '<div class="wrap">';
            echo '<h1>授权失败</h1>';
            echo '<div class="notice notice-error">';
            echo '<p><strong>错误:</strong> ' . esc_html($error) . '</p>';
            if ($error_description) {
                echo '<p><strong>描述:</strong> ' . esc_html($error_description) . '</p>';
            }
            echo '</div>';
            echo '<p><a href="' . admin_url('options-general.php?page=smart-twitter-publisher') . '" class="button">返回设置页面</a></p>';
            echo '</div>';
            return;
        }

        if (!isset($_GET['code']) || !isset($_GET['state'])) {
            return;
        }

        $code = sanitize_text_field($_GET['code']);
        $state = sanitize_text_field($_GET['state']);

        // 验证state
        $saved_state = get_option('stp_auth_state');
        if ($state !== $saved_state) {
            wp_die('授权状态验证失败，请重新授权');
        }

        // 获取访问令牌
        $result = $this->exchange_code_for_token($code);

        if ($result['success']) {
            // 清理临时数据
            delete_option('stp_auth_state');
            delete_option('stp_code_verifier');

            wp_redirect(admin_url('options-general.php?page=smart-twitter-publisher&authorized=1'));
            exit;
        } else {
            echo '<div class="wrap">';
            echo '<h1>授权失败</h1>';
            echo '<div class="notice notice-error">';
            echo '<p>' . esc_html($result['message']) . '</p>';
            echo '</div>';
            echo '<p><a href="' . admin_url('options-general.php?page=smart-twitter-publisher') . '" class="button">返回设置页面</a></p>';
            echo '</div>';
        }
    }

    private function generate_code_verifier() {
        return rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '=');
    }

    private function generate_code_challenge($verifier) {
        return rtrim(strtr(base64_encode(hash('sha256', $verifier, true)), '+/', '-_'), '=');
    }

    private function exchange_code_for_token($code) {
        $client_id = get_option('stp_client_id');
        $client_secret = get_option('stp_client_secret');
        $code_verifier = get_option('stp_code_verifier');
        $callback_url = admin_url('options-general.php?page=smart-twitter-publisher&callback=1');

        $token_url = 'https://api.x.com/2/oauth2/token';

        $data = http_build_query(array(
            'code' => $code,
            'grant_type' => 'authorization_code',
            'client_id' => $client_id,
            'redirect_uri' => $callback_url,
            'code_verifier' => $code_verifier
        ));

        $headers = array(
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: Basic ' . base64_encode($client_id . ':' . $client_secret)
        );

        $result = $this->make_twitter_request($token_url, 'POST', $data, $headers);

        if (!$result['success']) {
            return array(
                'success' => false,
                'message' => '网络请求失败: ' . $result['message']
            );
        }

        if ($result['http_code'] !== 200) {
            return array(
                'success' => false,
                'message' => 'Twitter API错误 (HTTP ' . $result['http_code'] . '): ' . $result['body']
            );
        }

        $token_data = json_decode($result['body'], true);

        if (!isset($token_data['access_token'])) {
            return array(
                'success' => false,
                'message' => '无法获取访问令牌: ' . $result['body']
            );
        }

        // 保存令牌
        update_option('stp_access_token', $token_data['access_token']);
        if (isset($token_data['refresh_token'])) {
            update_option('stp_refresh_token', $token_data['refresh_token']);
        }
        if (isset($token_data['expires_in'])) {
            update_option('stp_token_expires', time() + intval($token_data['expires_in']));
        }

        return array('success' => true);
    }

    public function publish_to_twitter($post_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'stp_twitter_posts';

        $post = get_post($post_id);
        if (!$post) {
            error_log('STP: 文章不存在，ID: ' . $post_id);
            return;
        }

        // 检查是否已经发布过
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE post_id = %d AND status = 'success'",
            $post_id
        ));

        if ($existing) {
            error_log('STP: 文章已发布过，跳过，ID: ' . $post_id);
            return;
        }

        // 获取发布间隔设置
        $publish_interval = get_option('stp_publish_interval', '5');
        $duplicate_check = get_option('stp_duplicate_check', '1');

        // 准备发布内容
        $custom_message = get_post_meta($post_id, '_stp_custom_message', true);
        $include_image = get_post_meta($post_id, '_stp_include_image', true);

        if ($custom_message) {
            $message = str_replace(
                array('{title}', '{url}'),
                array($post->post_title, get_permalink($post_id)),
                $custom_message
            );
        } else {
            $message = $post->post_title . ' ' . get_permalink($post_id);
        }

        // 限制消息长度（Twitter限制280字符）
        if (mb_strlen($message) > 270) {
            $message = mb_substr($message, 0, 270) . '...';
        }

        // 重复内容检测
        if ($duplicate_check === '1') {
            $similar = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE message = %s AND status = 'success' LIMIT 1",
                $message
            ));

            if ($similar) {
                error_log('STP: 检测到重复内容，跳过发布，文章ID: ' . $post_id);
                return;
            }
        }

        // 记录发布尝试
        $record_id = $wpdb->insert(
            $table_name,
            array(
                'post_id' => $post_id,
                'message' => $message,
                'status' => 'pending'
            )
        );

        if (!$record_id) {
            error_log('STP: 无法创建发布记录');
            return;
        }

        $record_id = $wpdb->insert_id;

        // 检查发布间隔逻辑
        if ($publish_interval > 0) {
            // 获取最后一次成功发布的时间
            $last_publish = $wpdb->get_var(
                "SELECT MAX(updated_at) FROM $table_name WHERE status = 'success'"
            );

            if ($last_publish) {
                $last_publish_time = strtotime($last_publish);
                $current_time = time();
                $time_diff = $current_time - $last_publish_time;
                $required_interval = $publish_interval * 60; // 转换为秒

                if ($time_diff < $required_interval) {
                    // 需要延迟发布
                    $delay_seconds = $required_interval - $time_diff;
                    $schedule_time = $current_time + $delay_seconds;
                    wp_schedule_single_event($schedule_time, 'stp_delayed_publish', array($record_id, $message, $include_image ? $post_id : null));

                    error_log('STP: 间隔不足，延迟发布，文章ID: ' . $post_id . ', 延迟: ' . round($delay_seconds/60, 1) . '分钟');
                    return;
                }
            }
            // 如果没有上次发布记录，或者间隔时间已够，立即发布
        }

        // 立即发布
        $result = $this->send_tweet($message, $include_image ? $post_id : null);
        $this->update_publish_result($record_id, $result);


    }

    private function send_tweet($text, $post_id_for_image = null) {
        $access_token = get_option('stp_access_token');
        if (!$access_token) {
            return array(
                'success' => false,
                'message' => '未授权Twitter访问'
            );
        }

        $tweet_data = array('text' => $text);

        // 如果需要包含图片
        if ($post_id_for_image) {
            $media_id = $this->upload_media($post_id_for_image);
            if ($media_id) {
                $tweet_data['media'] = array('media_ids' => array($media_id));
            }
        }

        $headers = array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access_token
        );

        // 尝试多个API端点，提高成功率
        $api_endpoints = array(
            'https://api.x.com/2/tweets',
            'https://api.twitter.com/2/tweets'
        );

        $last_error = '';

        foreach ($api_endpoints as $endpoint) {
            error_log('STP: 尝试发布到 ' . $endpoint);

            $result = $this->make_twitter_request($endpoint, 'POST', json_encode($tweet_data), $headers);

            if ($result['success'] && $result['http_code'] === 201) {
                $data = json_decode($result['body'], true);
                if (isset($data['data']['id'])) {
                    error_log('STP: Twitter发布成功，推文ID: ' . $data['data']['id']);
                    return array(
                        'success' => true,
                        'tweet_id' => $data['data']['id']
                    );
                }
            }

            $last_error = $result['message'] ?? 'Unknown error';
            error_log('STP: ' . $endpoint . ' 发布失败: ' . $last_error . ' (HTTP ' . ($result['http_code'] ?? 0) . ')');

            // 如果是SSL错误，等待2秒后重试下一个端点
            if (strpos($last_error, 'SSL') !== false || strpos($last_error, 'Connection reset') !== false) {
                sleep(2);
            }
        }

        return array(
            'success' => false,
            'message' => '所有API端点均失败，最后错误: ' . $last_error
        );
    }

    private function upload_media($post_id) {
        $thumbnail_id = get_post_thumbnail_id($post_id);
        if (!$thumbnail_id) {
            return null;
        }

        $image_path = get_attached_file($thumbnail_id);
        if (!$image_path || !file_exists($image_path)) {
            return null;
        }

        // 检查文件大小（Twitter限制5MB）
        if (filesize($image_path) > 5 * 1024 * 1024) {
            return null;
        }

        $access_token = get_option('stp_access_token');
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        $args = array(
            'method' => 'POST',
            'timeout' => 60,
            'sslverify' => false,
            'proxy' => 'socks5://' . $proxy_host . ':' . $proxy_port,
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token,
                'User-Agent' => 'Smart Twitter Publisher/1.0'
            ),
            'body' => array(
                'media' => new CURLFile($image_path)
            )
        );

        $response = wp_remote_post('https://upload.twitter.com/1.1/media/upload.json', $args);

        if (is_wp_error($response)) {
            return null;
        }

        $code = wp_remote_retrieve_response_code($response);
        if ($code === 200) {
            $data = json_decode(wp_remote_retrieve_body($response), true);
            if (isset($data['media_id_string'])) {
                return $data['media_id_string'];
            }
        }

        return null;
    }

    private function test_curl_socks5_proxy($proxy_host, $proxy_port) {
        if (!function_exists('curl_init')) {
            return '❌ cURL扩展未安装';
        }

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://httpbin.org/ip',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
            CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
            CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            CURLOPT_USERAGENT => 'Smart Twitter Publisher/1.0'
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return '❌ cURL SOCKS5代理失败: ' . $error;
        }

        if ($http_code === 200) {
            $data = json_decode($response, true);
            if (isset($data['origin'])) {
                return '✅ cURL SOCKS5代理成功，IP: ' . $data['origin'];
            } else {
                return '✅ cURL SOCKS5代理成功，HTTP: ' . $http_code;
            }
        } else {
            return '⚠️ cURL SOCKS5代理HTTP错误: ' . $http_code;
        }
    }

    private function make_twitter_request($url, $method = 'GET', $data = null, $headers = array()) {
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        if (!function_exists('curl_init')) {
            return array(
                'success' => false,
                'message' => 'cURL扩展未安装'
            );
        }

        $ch = curl_init();

        $default_headers = array(
            'User-Agent: Smart Twitter Publisher/1.0'
        );

        $all_headers = array_merge($default_headers, $headers);

        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 45, // 增加超时时间
            CURLOPT_CONNECTTIMEOUT => 30, // 连接超时
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2, // 强制使用TLS 1.2
            CURLOPT_SSL_CIPHER_LIST => 'ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS', // 指定加密套件
            CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
            CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            CURLOPT_HTTPPROXYTUNNEL => 1, // 确保代理隧道连接
            CURLOPT_FOLLOWLOCATION => false, // 不跟随重定向
            CURLOPT_MAXREDIRS => 0,
            CURLOPT_TCP_KEEPALIVE => 1, // 启用TCP保活
            CURLOPT_TCP_KEEPIDLE => 120,
            CURLOPT_TCP_KEEPINTVL => 60,
            CURLOPT_HTTPHEADER => $all_headers
        ));

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return array(
                'success' => false,
                'message' => 'cURL错误: ' . $error,
                'http_code' => 0
            );
        }

        return array(
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'body' => $response,
            'message' => $http_code >= 200 && $http_code < 300 ? 'Success' : 'HTTP Error ' . $http_code
        );
    }

    private function test_twitter_api_with_ssl($ssl_version) {
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        if (!function_exists('curl_init')) {
            return array(
                'success' => false,
                'message' => 'cURL扩展未安装',
                'http_code' => 0
            );
        }

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://api.x.com/2/tweets/search/recent?query=hello&max_results=10',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSLVERSION => $ssl_version,
            CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
            CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            CURLOPT_USERAGENT => 'Smart Twitter Publisher/1.0',
            CURLOPT_HTTPHEADER => array(
                'User-Agent: Smart Twitter Publisher/1.0'
            )
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return array(
                'success' => false,
                'message' => 'cURL错误: ' . $error,
                'http_code' => $http_code
            );
        }

        return array(
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'body' => $response,
            'message' => $http_code >= 200 && $http_code < 300 ? 'Success' : 'HTTP Error ' . $http_code
        );
    }

    private function test_twitter_api_endpoint($endpoint_url, $ssl_version) {
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        if (!function_exists('curl_init')) {
            return array(
                'success' => false,
                'message' => 'cURL扩展未安装',
                'http_code' => 0
            );
        }

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $endpoint_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSLVERSION => $ssl_version,
            CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
            CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            CURLOPT_USERAGENT => 'Smart Twitter Publisher/1.0',
            CURLOPT_HTTPHEADER => array(
                'User-Agent: Smart Twitter Publisher/1.0'
            )
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return array(
                'success' => false,
                'message' => 'cURL错误: ' . $error,
                'http_code' => $http_code
            );
        }

        return array(
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'body' => $response,
            'message' => $http_code >= 200 && $http_code < 300 ? 'Success' : 'HTTP Error ' . $http_code
        );
    }

    private function test_https_through_proxy() {
        $proxy_host = get_option('stp_proxy_host', '************');
        $proxy_port = get_option('stp_proxy_port', '7890');

        if (!function_exists('curl_init')) {
            return '❌ cURL扩展未安装';
        }

        // 测试访问GitHub API（通常比较稳定）
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://api.github.com/zen',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT,
            CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
            CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
            CURLOPT_USERAGENT => 'Smart Twitter Publisher/1.0'
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return '❌ 代理HTTPS测试失败: ' . $error;
        }

        if ($http_code === 200) {
            return '✅ 代理HTTPS连接正常 (GitHub API)';
        } else {
            return '⚠️ 代理HTTPS异常，HTTP: ' . $http_code;
        }
    }

    public function handle_delayed_publish($record_id, $message, $post_id_for_image = null) {
        $result = $this->send_tweet($message, $post_id_for_image);
        $this->update_publish_result($record_id, $result);
    }

    private function update_publish_result($record_id, $result) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'stp_twitter_posts';

        if ($result['success']) {
            // 更新为成功状态
            $wpdb->update(
                $table_name,
                array(
                    'status' => 'success',
                    'twitter_id' => $result['tweet_id']
                ),
                array('id' => $record_id)
            );

            error_log('STP: Twitter发布成功，记录ID: ' . $record_id . ', Twitter ID: ' . $result['tweet_id']);
        } else {
            // 更新为失败状态
            $wpdb->update(
                $table_name,
                array(
                    'status' => 'failed',
                    'error_message' => $result['message'],
                    'retry_count' => 1
                ),
                array('id' => $record_id)
            );

            error_log('STP: Twitter发布失败，记录ID: ' . $record_id . ', 错误: ' . $result['message']);
        }
    }

    private function show_system_logs() {
        // 显示WordPress错误日志中的STP相关记录
        $log_file = WP_CONTENT_DIR . '/debug.log';

        if (!file_exists($log_file)) {
            echo '<div class="notice notice-warning"><p>⚠️ 未找到debug.log文件。请在wp-config.php中启用调试日志：</p>';
            echo '<code>define("WP_DEBUG", true);<br>define("WP_DEBUG_LOG", true);</code></div>';
            return;
        }

        // 读取日志文件的最后1000行
        $lines = $this->tail($log_file, 1000);
        $stp_logs = array();

        foreach ($lines as $line) {
            if (strpos($line, 'STP:') !== false) {
                $stp_logs[] = $line;
            }
        }

        if (empty($stp_logs)) {
            echo '<div class="notice notice-info"><p>📝 暂无Twitter发布日志记录</p></div>';
            return;
        }

        // 只显示最近的50条记录
        $stp_logs = array_slice(array_reverse($stp_logs), 0, 50);

        echo '<div style="background: #f1f1f1; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">';
        echo '<h4>📋 最近50条Twitter发布日志：</h4>';
        echo '<pre style="font-size: 12px; line-height: 1.4;">';

        foreach ($stp_logs as $log) {
            // 高亮不同类型的日志
            if (strpos($log, '成功') !== false) {
                echo '<span style="color: green;">✅ ' . esc_html($log) . '</span>' . "\n";
            } elseif (strpos($log, '失败') !== false) {
                echo '<span style="color: red;">❌ ' . esc_html($log) . '</span>' . "\n";
            } elseif (strpos($log, '延迟') !== false) {
                echo '<span style="color: orange;">⏳ ' . esc_html($log) . '</span>' . "\n";
            } else {
                echo '<span style="color: #666;">ℹ️ ' . esc_html($log) . '</span>' . "\n";
            }
        }

        echo '</pre>';
        echo '</div>';

        echo '<p><strong>完整日志文件位置：</strong> <code>' . $log_file . '</code></p>';
    }

    private function tail($file, $lines = 1000) {
        $handle = fopen($file, "r");
        if (!$handle) {
            return array();
        }

        $linecounter = $lines;
        $pos = -2;
        $beginning = false;
        $text = array();

        while ($linecounter > 0) {
            $t = " ";
            while ($t != "\n") {
                if (fseek($handle, $pos, SEEK_END) == -1) {
                    $beginning = true;
                    break;
                }
                $t = fgetc($handle);
                $pos--;
            }
            $linecounter--;
            if ($beginning) {
                rewind($handle);
            }
            $text[$lines - $linecounter - 1] = fgets($handle);
            if ($beginning) break;
        }
        fclose($handle);
        return array_reverse($text);
    }
}

// 初始化插件
new SmartTwitterPublisher();
?>
