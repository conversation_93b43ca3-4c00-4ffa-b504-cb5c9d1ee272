<?php
/*
Plugin Name: Fix Zibll Admin
Description: 修复Zibll主题后台样式问题
Version: 1.0
Author: Claude
*/

if (!defined('ABSPATH')) {
    exit;
}

// 移除原有的样式和脚本
function fix_zibll_admin_styles() {
    global $wp_styles, $wp_scripts;
    
    // 如果在管理页面
    if (is_admin()) {
        // 移除可能有问题的样式
        wp_deregister_style('csf');
        wp_deregister_style('csf-rtl');
        wp_deregister_script('csf');
        wp_deregister_script('csf-plugins');
        
        // 重新注册正确的样式和脚本
        $theme_url = get_template_directory_uri();
        $min = '.min';
        
        // 重新注册主样式
        wp_register_style('csf', $theme_url . '/inc/csf-framework/assets/css/style' . $min . '.css', array(), time(), 'all');
        wp_enqueue_style('csf');
        
        // 如果是RTL布局
        if (is_rtl()) {
            wp_register_style('csf-rtl', $theme_url . '/inc/csf-framework/assets/css/style-rtl' . $min . '.css', array(), time(), 'all');
            wp_enqueue_style('csf-rtl');
        }
        
        // 重新注册脚本
        wp_register_script('csf-plugins', $theme_url . '/inc/csf-framework/assets/js/plugins' . $min . '.js', array('jquery'), time(), true);
        wp_enqueue_script('csf-plugins');
        
        wp_register_script('csf', $theme_url . '/inc/csf-framework/assets/js/main' . $min . '.js', array('csf-plugins'), time(), true);
        wp_enqueue_script('csf');
        
        // 本地化脚本
        wp_localize_script('csf', 'csf_vars', array(
            'color_palette' => array(),
            'i18n' => array(
                'confirm' => __('Are you sure?', 'csf'),
                'typing_text' => __('Please enter %s or more characters', 'csf'),
                'searching_text' => __('Searching...', 'csf'),
                'no_results_text' => __('No results found.', 'csf'),
            ),
        ));
    }
}

// 在admin_enqueue_scripts钩子上使用较高优先级
add_action('admin_enqueue_scripts', 'fix_zibll_admin_styles', 99); 