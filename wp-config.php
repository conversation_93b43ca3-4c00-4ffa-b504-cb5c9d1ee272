<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'wczkzk_chatgptcnnet' );

/** Database username */
define( 'DB_USER', 'wczkzk_chatgptcnnet' );

/** Database password */
define( 'DB_PASSWORD', '7Rb3bKXypy6wfBP2' );

/** Database hostname */
define( 'DB_HOST', 'localhost' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', false );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         ' Uly#ayjbe^D3Th .0gY<keV!q<+:mr%fK=~.s4:h<rO@H&hr=2@`R7N.T/k_M-V' );
define( 'SECURE_AUTH_KEY',  '6rT,cXMwLe]=<MQG<l0a@qumcB(Fs+U3`h?e:X@a~:traP_#TRQQYRxsAx=>N,Vh' );
define( 'LOGGED_IN_KEY',    't:JM?>~%11+S~HeonD|AZ/c ;[^ZvlX@{Z~ov^MM0QP-zKaZ#BRxf5<Vvh~blaDj' );
define( 'NONCE_KEY',        '.)xMz5[Oq2F%*I|``&5Nr% DX*L.V^6LK2CtrSP8M,x81ujk<is])[wO^I@j[X9~' );
define( 'AUTH_SALT',        'hcfKS&fnMJp`kXgK}=$]qmChQHDDqW){q*C[Y@ge7I(KMa<AgU(>!3e3@+8{}&.i' );
define( 'SECURE_AUTH_SALT', 'JVC6J?6!66<mkTd<;/>/UIB*0_GHrcxOpj.Xcs q^$`J$7O,2C.9syqM-qTlP_HP' );
define( 'LOGGED_IN_SALT',   '|gz8Pq>%1rJZ`>-ALT:Z4PTLy2I_~w9urZjuglI,AY$2-+u(2}(K<b2Ig[5Fdm`/' );
define( 'NONCE_SALT',       'a[p*tp|*%,Fr==+x7KZeW?3BQ[wCTV88][pVz#E>w$t+0k8._y7y^$2tq{[T2<5>' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'wp_';

/* Add any custom values between this line and the "stop editing" line. */


// 代理设置
// define('WP_PROXY_HOST', '************');
// define('WP_PROXY_PORT', '7890');

// 设置SOCKS5代理类型（在WordPress加载完成后执行）
// define('WP_PROXY_SOCKS5', true);

// 修复数据盘挂载后的路径问题
define('WP_SITEURL', 'https://zk.chatgptcnnet.top');
define('WP_HOME', 'https://zk.chatgptcnnet.top');

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';