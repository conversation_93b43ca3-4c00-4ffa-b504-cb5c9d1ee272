<?php
/**
 * Plugin Name: Simple Twitter Publisher
 * Plugin URI: https://your-site.com
 * Description: 简单可靠的Twitter自动发布插件，专为中国服务器环境优化
 * Version: 1.0.0
 * Author: Custom Development
 * License: GPL v2 or later
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('STP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STP_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STP_VERSION', '1.0.0');

// 插件激活时创建数据表
register_activation_hook(__FILE__, 'stp_create_tables');

function stp_create_tables() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'twitter_posts';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id bigint(20) NOT NULL,
        twitter_id varchar(100) DEFAULT '',
        status varchar(20) DEFAULT 'pending',
        message text,
        error_message text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY post_id (post_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

// 加载插件类
class SimpleTwitterPublisher {
    
    private $proxy_host = '************';
    private $proxy_port = 7890;
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_post_meta'));
        add_action('transition_post_status', array($this, 'handle_post_publish'), 10, 3);
        add_action('wp_ajax_stp_test_connection', array($this, 'test_connection'));
        add_action('wp_ajax_stp_authorize', array($this, 'handle_authorization'));
    }
    
    public function init() {
        // 设置默认选项
        if (!get_option('stp_proxy_host')) {
            update_option('stp_proxy_host', $this->proxy_host);
            update_option('stp_proxy_port', $this->proxy_port);
            update_option('stp_ssl_verify', '0');
        }
    }
    
    public function admin_menu() {
        add_options_page(
            'Twitter发布设置',
            'Twitter发布',
            'manage_options',
            'simple-twitter-publisher',
            array($this, 'admin_page')
        );
    }
    
    public function add_meta_boxes() {
        add_meta_box(
            'stp_twitter_box',
            'Twitter发布',
            array($this, 'meta_box_callback'),
            'post',
            'side',
            'high'
        );
    }
    
    public function meta_box_callback($post) {
        wp_nonce_field('stp_meta_box', 'stp_meta_box_nonce');
        
        $publish_to_twitter = get_post_meta($post->ID, '_stp_publish_to_twitter', true);
        $custom_message = get_post_meta($post->ID, '_stp_custom_message', true);
        
        echo '<p>';
        echo '<label>';
        echo '<input type="checkbox" name="stp_publish_to_twitter" value="1" ' . checked($publish_to_twitter, '1', false) . '>';
        echo ' 发布到Twitter';
        echo '</label>';
        echo '</p>';
        
        echo '<p>';
        echo '<label for="stp_custom_message">自定义消息:</label><br>';
        echo '<textarea name="stp_custom_message" id="stp_custom_message" rows="3" style="width:100%;">' . esc_textarea($custom_message) . '</textarea>';
        echo '<small>留空则使用文章标题+链接</small>';
        echo '</p>';
    }
    
    public function save_post_meta($post_id) {
        if (!isset($_POST['stp_meta_box_nonce']) || !wp_verify_nonce($_POST['stp_meta_box_nonce'], 'stp_meta_box')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        $publish_to_twitter = isset($_POST['stp_publish_to_twitter']) ? '1' : '0';
        $custom_message = sanitize_textarea_field($_POST['stp_custom_message']);
        
        update_post_meta($post_id, '_stp_publish_to_twitter', $publish_to_twitter);
        update_post_meta($post_id, '_stp_custom_message', $custom_message);
    }
    
    public function handle_post_publish($new_status, $old_status, $post) {
        // 只处理文章发布
        if ($post->post_type !== 'post' || $new_status !== 'publish' || $old_status === 'publish') {
            return;
        }
        
        // 检查是否需要发布到Twitter
        $publish_to_twitter = get_post_meta($post->ID, '_stp_publish_to_twitter', true);
        if ($publish_to_twitter !== '1') {
            return;
        }
        
        // 异步发布到Twitter
        wp_schedule_single_event(time() + 10, 'stp_publish_to_twitter', array($post->ID));
    }
    
    public function admin_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        $client_id = get_option('stp_client_id', '');
        $client_secret = get_option('stp_client_secret', '');
        $access_token = get_option('stp_access_token', '');
        $proxy_host = get_option('stp_proxy_host', $this->proxy_host);
        $proxy_port = get_option('stp_proxy_port', $this->proxy_port);
        
        ?>
        <div class="wrap">
            <h1>Twitter发布设置</h1>
            
            <div class="notice notice-info">
                <p><strong>使用说明：</strong></p>
                <ul>
                    <li>1. 在Twitter Developer Portal创建应用获取API密钥</li>
                    <li>2. 填写下面的Client ID和Client Secret</li>
                    <li>3. 点击"获取访问令牌"完成授权</li>
                    <li>4. 发布文章时勾选"发布到Twitter"即可自动同步</li>
                </ul>
            </div>
            
            <form method="post" action="">
                <?php wp_nonce_field('stp_settings', 'stp_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Client ID</th>
                        <td>
                            <input type="text" name="stp_client_id" value="<?php echo esc_attr($client_id); ?>" class="regular-text" />
                            <p class="description">从Twitter Developer Portal获取</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Client Secret</th>
                        <td>
                            <input type="password" name="stp_client_secret" value="<?php echo esc_attr($client_secret); ?>" class="regular-text" />
                            <p class="description">从Twitter Developer Portal获取</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">代理服务器</th>
                        <td>
                            <input type="text" name="stp_proxy_host" value="<?php echo esc_attr($proxy_host); ?>" class="regular-text" />
                            <p class="description">SOCKS5代理主机地址</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">代理端口</th>
                        <td>
                            <input type="number" name="stp_proxy_port" value="<?php echo esc_attr($proxy_port); ?>" class="small-text" />
                            <p class="description">SOCKS5代理端口</p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button('保存设置'); ?>
            </form>
            
            <hr>
            
            <h2>授权状态</h2>
            <?php if ($access_token): ?>
                <div class="notice notice-success">
                    <p>✅ Twitter授权已完成</p>
                </div>
                <p>
                    <button type="button" class="button" onclick="location.href='<?php echo admin_url('options-general.php?page=simple-twitter-publisher&revoke=1'); ?>'">撤销授权</button>
                </p>
            <?php else: ?>
                <div class="notice notice-warning">
                    <p>⚠️ 尚未完成Twitter授权</p>
                </div>
                <?php if ($client_id && $client_secret): ?>
                    <p>
                        <button type="button" class="button button-primary" onclick="startAuthorization()">获取访问令牌</button>
                        <button type="button" class="button" onclick="testConnection()">测试连接</button>
                    </p>
                <?php else: ?>
                    <p class="description">请先填写Client ID和Client Secret</p>
                <?php endif; ?>
            <?php endif; ?>
            
            <div id="test-result" style="margin-top: 10px;"></div>
        </div>
        
        <script>
        function testConnection() {
            document.getElementById('test-result').innerHTML = '<p>正在测试连接...</p>';
            
            fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=stp_test_connection&nonce=<?php echo wp_create_nonce('stp_test'); ?>'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('test-result').innerHTML = '<div class="notice notice-success"><p>✅ ' + data.data + '</p></div>';
                } else {
                    document.getElementById('test-result').innerHTML = '<div class="notice notice-error"><p>❌ ' + data.data + '</p></div>';
                }
            })
            .catch(error => {
                document.getElementById('test-result').innerHTML = '<div class="notice notice-error"><p>❌ 连接测试失败</p></div>';
            });
        }
        
        function startAuthorization() {
            alert('授权功能正在开发中，请稍后...');
        }
        </script>
        <?php
    }
    
    public function save_settings() {
        if (!isset($_POST['stp_settings_nonce']) || !wp_verify_nonce($_POST['stp_settings_nonce'], 'stp_settings')) {
            return;
        }
        
        update_option('stp_client_id', sanitize_text_field($_POST['stp_client_id']));
        update_option('stp_client_secret', sanitize_text_field($_POST['stp_client_secret']));
        update_option('stp_proxy_host', sanitize_text_field($_POST['stp_proxy_host']));
        update_option('stp_proxy_port', intval($_POST['stp_proxy_port']));
        
        echo '<div class="notice notice-success"><p>设置已保存</p></div>';
    }
    
    public function test_connection() {
        check_ajax_referer('stp_test', 'nonce');
        
        $proxy_host = get_option('stp_proxy_host', $this->proxy_host);
        $proxy_port = get_option('stp_proxy_port', $this->proxy_port);
        
        // 测试代理连接
        $ch = curl_init('https://httpbin.org/ip');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_PROXY, $proxy_host);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $result = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($error) {
            wp_send_json_error('代理连接失败: ' . $error);
        } else {
            wp_send_json_success('代理连接成功! HTTP状态码: ' . $httpCode);
        }
    }
}

// 初始化插件
new SimpleTwitterPublisher();

// 注册定时任务
add_action('stp_publish_to_twitter', 'stp_do_publish_to_twitter');

function stp_do_publish_to_twitter($post_id) {
    // 这里将实现实际的Twitter发布逻辑
    // 暂时记录到数据库
    global $wpdb;
    
    $post = get_post($post_id);
    if (!$post) return;
    
    $custom_message = get_post_meta($post_id, '_stp_custom_message', true);
    $message = $custom_message ? $custom_message : $post->post_title . ' ' . get_permalink($post_id);
    
    $table_name = $wpdb->prefix . 'twitter_posts';
    
    $wpdb->insert(
        $table_name,
        array(
            'post_id' => $post_id,
            'message' => $message,
            'status' => 'pending'
        )
    );
    
    // TODO: 实现实际的Twitter API调用
    error_log('Twitter发布任务已创建: ' . $message);
}
?>
