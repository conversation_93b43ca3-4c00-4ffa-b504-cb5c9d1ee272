<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== Twitter发布调试信息 ===\n";

// 检查定时任务
$scheduled_events = wp_get_scheduled_event('stp_delayed_publish');
if ($scheduled_events) {
    echo "✅ 找到定时任务:\n";
    echo "- 时间戳: " . $scheduled_events->timestamp . "\n";
    echo "- 计划时间: " . date('Y-m-d H:i:s', $scheduled_events->timestamp) . "\n";
    echo "- 当前时间: " . date('Y-m-d H:i:s', current_time('timestamp')) . "\n";
    echo "- 剩余时间: " . round(($scheduled_events->timestamp - current_time('timestamp'))/60, 1) . " 分钟\n";
} else {
    echo "❌ 没有找到定时任务\n";
}

// 检查所有相关的定时任务
$all_crons = wp_get_ready_cron_jobs();
echo "\n=== 所有准备执行的定时任务 ===\n";
foreach ($all_crons as $timestamp => $cron) {
    foreach ($cron as $hook => $events) {
        if (strpos($hook, 'stp_') === 0) {
            echo "- Hook: $hook, 时间: " . date('Y-m-d H:i:s', $timestamp) . "\n";
        }
    }
}

// 检查数据库中的最新记录
global $wpdb;
$table_name = $wpdb->prefix . 'twitter_posts';
$latest_records = $wpdb->get_results("SELECT * FROM $table_name ORDER BY updated_at DESC LIMIT 5");

echo "\n=== 数据库最新记录 ===\n";
foreach ($latest_records as $record) {
    echo "- ID: {$record->id}, 文章ID: {$record->post_id}, 状态: {$record->status}, 时间: {$record->updated_at}\n";
}

// 检查WordPress定时任务系统
echo "\n=== WordPress定时任务系统状态 ===\n";
echo "- 当前时间: " . current_time('mysql') . "\n";
echo "- UTC时间: " . gmdate('Y-m-d H:i:s') . "\n";
echo "- 时区: " . get_option('timezone_string') . "\n";
echo "- GMT偏移: " . get_option('gmt_offset') . "\n";

// 检查是否有卡住的定时任务
$stuck_crons = wp_get_scheduled_event('stp_delayed_publish');
if ($stuck_crons && ($stuck_crons->timestamp < current_time('timestamp') - 3600)) {
    echo "⚠️ 发现卡住的定时任务，建议清理\n";
    echo "<a href='?clear_stuck=1'>点击清理卡住的任务</a>\n";
}

// 清理卡住的任务
if (isset($_GET['clear_stuck'])) {
    wp_clear_scheduled_hook('stp_delayed_publish');
    echo "✅ 已清理所有Twitter发布定时任务\n";
    echo "<script>setTimeout(function(){location.reload();}, 2000);</script>\n";
}

echo "\n=== 手动操作 ===\n";
echo "<a href='?manual_trigger=1'>手动触发一次Twitter发布检查</a><br>\n";
echo "<a href='?clear_all_crons=1'>清理所有Twitter定时任务</a><br>\n";

if (isset($_GET['manual_trigger'])) {
    // 手动触发发布检查
    if (class_exists('SmartTwitterPublisher')) {
        echo "正在手动触发Twitter发布检查...\n";

        // 获取插件实例并手动触发检查
        global $smart_twitter_publisher;
        if ($smart_twitter_publisher) {
            $smart_twitter_publisher->check_pending_publications();
            echo "✅ 手动触发完成，请检查日志\n";
        } else {
            echo "❌ 无法获取插件实例\n";
        }
    }
}

// 清理所有Twitter定时任务
if (isset($_GET['clear_all_crons'])) {
    wp_clear_scheduled_hook('stp_delayed_publish');
    wp_clear_scheduled_hook('stp_publish_to_twitter');
    echo "✅ 已清理所有Twitter定时任务\n";
    echo "<script>setTimeout(function(){location.reload();}, 2000);</script>\n";
}
?>
