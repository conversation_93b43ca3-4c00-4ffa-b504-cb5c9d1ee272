<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== SSL连接修复工具 ===\n";

$proxy_host = get_option('stp_proxy_host', '************');
$proxy_port = get_option('stp_proxy_port', '7890');

echo "测试代理: $proxy_host:$proxy_port\n\n";

// 测试不同的SSL配置
$ssl_configs = [
    'TLS 1.2 + 强加密' => [
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
        CURLOPT_SSL_CIPHER_LIST => 'ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS'
    ],
    'TLS 1.3' => [
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_3
    ],
    'TLS 1.2 基础' => [
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
    ],
    '自动协商' => [
        CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT
    ]
];

foreach ($ssl_configs as $config_name => $ssl_options) {
    echo "=== 测试配置: $config_name ===\n";
    
    $ch = curl_init();
    $base_options = [
        CURLOPT_URL => 'https://api.x.com/2/users/me',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer test-token',
            'Content-Type: application/json',
            'User-Agent: Smart Twitter Publisher/1.0'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_VERBOSE => false
    ];
    
    // 合并SSL配置
    $options = array_merge($base_options, $ssl_options);
    curl_setopt_array($ch, $options);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $ssl_verify_result = curl_getinfo($ch, CURLINFO_SSL_VERIFYRESULT);
    
    curl_close($ch);
    
    $duration = round(($end_time - $start_time) * 1000, 2);
    
    if ($error) {
        echo "❌ 失败: $error (耗时: {$duration}ms)\n";
    } else {
        echo "✅ 成功: HTTP $http_code (耗时: {$duration}ms)\n";
        if ($http_code == 401) {
            echo "   认证失败（正常，使用测试token）\n";
        }
    }
    echo "\n";
}

// 测试不使用代理的HTTPS连接
echo "=== 测试直连HTTPS（不使用代理） ===\n";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'https://httpbin.org/get',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
]);

$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($end_time - $start_time) * 1000, 2);

if ($error) {
    echo "❌ 直连HTTPS失败: $error (耗时: {$duration}ms)\n";
    echo "✅ 确认需要使用代理\n";
} else {
    echo "✅ 直连HTTPS成功: HTTP $http_code (耗时: {$duration}ms)\n";
    echo "⚠️ 可能不需要代理，或代理配置有问题\n";
}

echo "\n=== 代理服务器状态检查 ===\n";

// 检查代理服务器本身
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://httpbin.org/ip',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_PROXY => $proxy_host . ':' . $proxy_port,
    CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5
]);

$response = curl_exec($ch);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 代理服务器连接失败: $error\n";
    echo "建议：检查AWS Korea服务器状态\n";
} else {
    $ip_data = json_decode($response, true);
    if (isset($ip_data['origin'])) {
        echo "✅ 代理服务器正常，出口IP: " . $ip_data['origin'] . "\n";
    }
}

echo "\n=== 修复建议 ===\n";
echo "1. 首先设置完整的API配置（API Key + API Secret）\n";
echo "2. 如果SSL连接问题持续，尝试重启AWS Korea代理服务器\n";
echo "3. 考虑更换代理端口（如7891、7892等）\n";
echo "4. 重新授权Twitter API获取新的Token\n";

echo "\n<a href='check-twitter-api.php'>返回API检查</a> | ";
echo "<a href='debug-twitter-schedule.php'>返回调试页面</a>\n";
?>
