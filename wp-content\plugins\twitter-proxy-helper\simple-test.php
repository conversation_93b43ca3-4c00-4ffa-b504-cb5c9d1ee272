<?php
/**
 * 简化版Twitter修复测试
 */

// 基本安全检查
if (!defined('ABSPATH')) {
    // 尝试加载WordPress
    $wp_load = dirname(dirname(dirname(__DIR__))) . '/wp-load.php';
    if (file_exists($wp_load)) {
        require_once($wp_load);
    } else {
        die('无法加载WordPress');
    }
}

// 权限检查
if (!current_user_can('manage_options')) {
    die('权限不足');
}

echo '<h1>Twitter修复状态检查</h1>';

// 1. 检查授权文件修复
echo '<h2>1. 授权文件修复状态</h2>';
$auth_file = ABSPATH . 'wp-content/plugins/twitter-auto-publish/admin/authorization.php';
if (file_exists($auth_file)) {
    $content = file_get_contents($auth_file);
    if (strpos($content, 'is_wp_error($response)') !== false) {
        echo '<p style="color:green;">✅ 授权文件已修复</p>';
    } else {
        echo '<p style="color:red;">❌ 授权文件未修复</p>';
    }
} else {
    echo '<p style="color:red;">❌ 授权文件不存在</p>';
}

// 2. 检查代理文件修复
echo '<h2>2. 代理文件修复状态</h2>';
$proxy_file = ABSPATH . 'wp-content/plugins/twitter-proxy-helper/twitter-proxy-helper.php';
if (file_exists($proxy_file)) {
    $content = file_get_contents($proxy_file);
    if (strpos($content, 'try {') !== false) {
        echo '<p style="color:green;">✅ 代理文件已修复</p>';
    } else {
        echo '<p style="color:orange;">⚠️ 代理文件可能需要检查</p>';
    }
} else {
    echo '<p style="color:red;">❌ 代理文件不存在</p>';
}

// 3. 检查插件状态
echo '<h2>3. 插件状态</h2>';
if (function_exists('is_plugin_active')) {
    if (is_plugin_active('twitter-auto-publish/twitter-auto-publish.php')) {
        echo '<p style="color:green;">✅ Twitter插件已激活</p>';
    } else {
        echo '<p style="color:red;">❌ Twitter插件未激活</p>';
    }
} else {
    echo '<p>无法检查插件状态</p>';
}

// 4. 简单连接测试
echo '<h2>4. 连接测试</h2>';
if (isset($_GET['test'])) {
    echo '<p>正在测试连接...</p>';
    
    $ch = curl_init('https://httpbin.org/ip');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_PROXY, '************:7890');
    curl_setopt($ch, CURLOPT_PROXYTYPE, 7);
    
    $result = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo '<p style="color:red;">连接失败: ' . $error . '</p>';
    } else {
        echo '<p style="color:green;">代理连接成功!</p>';
        echo '<pre>' . $result . '</pre>';
    }
}

echo '<p><a href="?test=1">点击测试代理连接</a></p>';

// 5. 最近错误
echo '<h2>5. 最近错误</h2>';
$log_file = ABSPATH . 'wp-content/debug.log';
if (file_exists($log_file)) {
    $lines = file($log_file);
    $recent = array_slice($lines, -10);
    $twitter_errors = array_filter($recent, function($line) {
        return stripos($line, 'twitter') !== false || stripos($line, 'setProxy') !== false;
    });
    
    if ($twitter_errors) {
        echo '<pre>' . implode('', $twitter_errors) . '</pre>';
    } else {
        echo '<p>没有发现最近的Twitter错误</p>';
    }
} else {
    echo '<p>调试日志不存在</p>';
}

echo '<hr><p><strong>建议：</strong>如果修复状态显示正常，请尝试发布一篇测试文章验证Twitter同步功能。</p>';
?>
