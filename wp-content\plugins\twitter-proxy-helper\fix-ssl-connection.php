<?php
/**
 * Twitter SSL连接修复脚本
 * 专门解决 "OpenSSL SSL_connect: Connection reset by peer" 错误
 */

// 基本安全检查
if (!defined('ABSPATH')) {
    $wp_load = dirname(dirname(dirname(__DIR__))) . '/wp-load.php';
    if (file_exists($wp_load)) {
        require_once($wp_load);
    } else {
        die('无法加载WordPress');
    }
}

if (!current_user_can('manage_options')) {
    die('权限不足');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Twitter SSL连接修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .button { padding: 8px 16px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; margin: 5px; display: inline-block; }
    </style>
</head>
<body>
    <h1>🔧 Twitter SSL连接修复工具</h1>
    
    <div class="section">
        <h2>📋 当前问题诊断</h2>
        <p><strong>错误信息：</strong> <code>OpenSSL SSL_connect: Connection reset by peer in connection to api.x.com:443</code></p>
        <p><strong>问题原因：</strong> SSL握手失败，通常是代理配置或SSL验证问题</p>
    </div>

    <div class="section">
        <h2>🔍 连接测试</h2>
        <?php
        if (isset($_POST['test_connection'])) {
            echo '<h3>正在测试不同的连接方式...</h3>';
            
            // 测试1: 直接连接（应该失败）
            echo '<h4>测试1: 直接连接到Twitter API</h4>';
            $ch = curl_init('https://api.x.com/2/openapi.json');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $result = curl_exec($ch);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($error) {
                echo '<p class="error">❌ 直接连接失败（预期结果）: ' . $error . '</p>';
            } else {
                echo '<p class="success">✅ 直接连接成功（意外）: HTTP ' . $httpCode . '</p>';
            }
            
            // 测试2: SOCKS5代理连接
            echo '<h4>测试2: 通过SOCKS5代理连接</h4>';
            $ch = curl_init('https://api.x.com/2/openapi.json');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_PROXY, '************');
            curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $start = microtime(true);
            $result = curl_exec($ch);
            $time = round((microtime(true) - $start) * 1000);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($error) {
                echo '<p class="error">❌ SOCKS5代理连接失败: ' . $error . '</p>';
            } else {
                echo '<p class="success">✅ SOCKS5代理连接成功: HTTP ' . $httpCode . ' (耗时: ' . $time . 'ms)</p>';
            }
            
            // 测试3: 使用WordPress HTTP API
            echo '<h4>测试3: WordPress HTTP API (修复后)</h4>';
            $response = wp_remote_get('https://api.x.com/2/openapi.json', [
                'timeout' => 30,
                'sslverify' => false,
                'proxy' => 'socks5://************:7890',
                'headers' => [
                    'User-Agent' => 'WordPress/' . get_bloginfo('version') . '; ' . get_bloginfo('url')
                ]
            ]);
            
            if (is_wp_error($response)) {
                echo '<p class="error">❌ WordPress HTTP API失败: ' . $response->get_error_message() . '</p>';
            } else {
                $code = wp_remote_retrieve_response_code($response);
                echo '<p class="success">✅ WordPress HTTP API成功: HTTP ' . $code . '</p>';
            }
        }
        ?>
        
        <form method="post">
            <button type="submit" name="test_connection" class="button">🧪 运行连接测试</button>
        </form>
    </div>

    <div class="section">
        <h2>🛠️ 自动修复</h2>
        <?php
        if (isset($_POST['apply_fix'])) {
            echo '<h3>正在应用修复...</h3>';
            
            // 修复1: 更新Twitter插件的SSL设置
            update_option('xyz_twap_peer_verification', '0');
            echo '<p class="success">✅ 已禁用Twitter插件的SSL验证</p>';
            
            // 修复2: 设置代理相关选项
            update_option('xyz_twap_proxy_enabled', '1');
            update_option('xyz_twap_proxy_host', '************');
            update_option('xyz_twap_proxy_port', '7890');
            update_option('xyz_twap_proxy_type', 'socks5');
            echo '<p class="success">✅ 已配置代理设置</p>';
            
            // 修复3: 清除可能的缓存
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
                echo '<p class="success">✅ 已清除缓存</p>';
            }
            
            echo '<p class="success"><strong>🎉 修复完成！请尝试重新授权Twitter或发布测试文章。</strong></p>';
        }
        ?>
        
        <form method="post">
            <button type="submit" name="apply_fix" class="button" onclick="return confirm('确定要应用SSL连接修复吗？')">🔧 应用修复</button>
        </form>
    </div>

    <div class="section">
        <h2>📝 手动修复步骤</h2>
        <ol>
            <li><strong>清除错误信息：</strong>
                <p>进入 Twitter Auto Publish 设置页面，如果看到错误信息，点击"清除错误"或重新授权</p>
            </li>
            
            <li><strong>重新授权：</strong>
                <p>在Twitter插件设置中，点击"重新授权"按钮，使用修复后的连接配置</p>
            </li>
            
            <li><strong>测试发布：</strong>
                <p>创建一篇测试文章，勾选"发布到Twitter"，观察是否还有SSL错误</p>
            </li>
        </ol>
    </div>

    <div class="section">
        <h2>🔗 快速链接</h2>
        <a href="<?php echo admin_url('admin.php?page=twitter-auto-publish-settings'); ?>" class="button">Twitter插件设置</a>
        <a href="<?php echo admin_url('post-new.php'); ?>" class="button">创建测试文章</a>
        <a href="<?php echo admin_url('tools.php?page=action-scheduler'); ?>" class="button">查看任务队列</a>
    </div>

    <div class="section">
        <h2>📊 修复状态检查</h2>
        <?php
        echo '<h4>当前配置状态：</h4>';
        echo '<ul>';
        echo '<li>SSL验证: ' . (get_option('xyz_twap_peer_verification') == '1' ? '<span class="error">启用</span>' : '<span class="success">禁用</span>') . '</li>';
        echo '<li>代理配置: ' . (get_option('xyz_twap_proxy_enabled') == '1' ? '<span class="success">启用</span>' : '<span class="warning">未启用</span>') . '</li>';
        
        $client_id = get_option('xyz_twap_twconsumer_id');
        $token = get_option('xyz_twap_tw_token');
        echo '<li>API凭据: ' . (!empty($client_id) ? '<span class="success">已配置</span>' : '<span class="error">未配置</span>') . '</li>';
        echo '<li>访问令牌: ' . (!empty($token) ? '<span class="success">已获取</span>' : '<span class="error">未获取</span>') . '</li>';
        echo '</ul>';
        ?>
    </div>
</body>
</html>
