<?php
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo "=== WordPress 路径调试信息 ===\n";
echo "Site URL: " . get_site_url() . "\n";
echo "Home URL: " . get_home_url() . "\n";
echo "Template Directory: " . get_template_directory() . "\n";
echo "Template Directory URI: " . get_template_directory_uri() . "\n";
echo "Stylesheet Directory: " . get_stylesheet_directory() . "\n";
echo "Stylesheet Directory URI: " . get_stylesheet_directory_uri() . "\n";
echo "ABSPATH: " . ABSPATH . "\n";
echo "WP_CONTENT_DIR: " . WP_CONTENT_DIR . "\n";
echo "WP_CONTENT_URL: " . WP_CONTENT_URL . "\n";

// 检查CSF框架路径
if (class_exists('CSF')) {
    echo "\n=== CSF 框架路径信息 ===\n";
    $reflection = new ReflectionClass('CSF');
    echo "CSF Class File: " . $reflection->getFileName() . "\n";
    
    if (property_exists('CSF', 'dir')) {
        echo "CSF::dir: " . CSF::$dir . "\n";
    }
    if (property_exists('CSF', 'url')) {
        echo "CSF::url: " . CSF::$url . "\n";
    }
}
?>
