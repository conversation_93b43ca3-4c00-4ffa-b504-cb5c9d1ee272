<?php
/**
 * 自定义功能代码
 * 主题更新时此文件不会被覆盖
 * 请注意PHP代码规范，错误的代码可能会导致网站无法正常运行
 */

// 完全禁用 REST API，但保留 miniapp 端点和必要的 WP API
add_filter('rest_authentication_errors', function($result) {
    if (!empty($result)) {
        return $result;
    }
    
    // 获取当前请求路径
    $current_route = trim(str_replace(rest_get_url_prefix(), '', $_SERVER['REQUEST_URI']), '/');
    
    // 允许的端点列表
    $allowed_routes = array(
        'miniapp/v1',      // 小程序 API
        'wp/v2/posts',     // 文章接口
        'wp/v2/categories', // 分类接口
        'wp/v2/pages',     // 页面接口
        'wp/v2/media',     // 媒体接口
        'wp/v2/tags'       // 标签接口
    );
    
    // 检查是否是允许的端点
    foreach ($allowed_routes as $route) {
        if (strpos($current_route, $route) === 0) {
            return true;
        }
    }
    
    // 如果是管理员则允许访问所有端点
    if (current_user_can('administrator')) {
        return true;
    }

    // 如果是后台管理页面，允许所有REST API请求
    if (is_admin()) {
        return true;
    }
    
    // 允许小程序特定的请求
    if (isset($_SERVER['HTTP_USER_AGENT']) && strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
        return true;
    }
    
    // 其他请求需要登录验证
    if (!is_user_logged_in()) {
        return new WP_Error(
            'rest_not_logged_in',
            __('You are not currently logged in.'),
            array('status' => 401)
        );
    }
    
    return $result;
});

// 移除 REST API 链接
remove_action('wp_head', 'rest_output_link_wp_head', 10);
remove_action('template_redirect', 'rest_output_link_header', 11);

// 禁用 XML-RPC
add_filter('xmlrpc_enabled', '__return_false');

// 添加安全头部
add_action('send_headers', function() {
    // 防止页面在iframe中被加载
    header('X-Frame-Options: SAMEORIGIN');
    // XSS 保护
    header('X-XSS-Protection: 1; mode=block');
    // 防止MIME类型嗅探
    header('X-Content-Type-Options: nosniff');
    // 引用策略
    header('Referrer-Policy: strict-origin-when-cross-origin');
});

// 记录 API 访问日志
add_action('rest_api_init', function() {
    if (!defined('REST_REQUEST') || !REST_REQUEST) {
        return;
    }
    
    $log_data = array(
        'time' => current_time('mysql'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'request_uri' => $_SERVER['REQUEST_URI'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'method' => $_SERVER['REQUEST_METHOD']
    );
    
    // 将日志写入文件
    $log_file = WP_CONTENT_DIR . '/rest-api-access.log';
    $log_line = json_encode($log_data) . "\n";
    file_put_contents($log_file, $log_line, FILE_APPEND);
});

// 添加 API 访问白名单
function is_ip_allowed($ip) {
    $allowed_ips = array(
        '127.0.0.1',        // 本地测试
        'YOUR_SERVER_IP',    // 替换为您的服务器IP
        'YOUR_CLIENT_IP'     // 替换为您的客户端IP
    );
    return in_array($ip, $allowed_ips);
}

// 优化 REST API 响应
add_filter('rest_pre_serve_request', function($served, $result, $request, $server) {
    // 添加跨域头
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept, Origin, Authorization');
    
    // 如果是预检请求，直接返回
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        status_header(200);
        exit();
    }
    
    // 添加缓存控制头
    if (strpos($request->get_route(), '/miniapp/v1/') === 0) {
        header('Cache-Control: private, max-age=3600');
    }
    
    return $served;
}, 10, 4);

// 移除不必要的限制
remove_filter('rest_pre_dispatch', 'rest_pre_dispatch', 10);

// 自定义 REST API 错误信息
add_filter('rest_authentication_errors', function($error) {
    if (is_wp_error($error)) {
        $error_data = $error->get_error_data();
        $status = isset($error_data['status']) ? $error_data['status'] : 401;
        
        return new WP_Error(
            'rest_auth_error',
            '访问受限，请确认您有权限访问该接口',
            array('status' => $status)
        );
    }
    return $error;
}, 99);

// 为小程序API添加额外的响应处理
add_filter('rest_pre_echo_response', function($response, $server, $request) {
    // 只处理小程序API的响应
    if (strpos($request->get_route(), '/miniapp/v1/') === 0) {
        // 如果响应是WP_Error，转换为统一的错误格式
        if (is_wp_error($response)) {
            return array(
                'code' => $response->get_error_code(),
                'message' => $response->get_error_message(),
                'data' => null
            );
        }
        
        // 如果是正常响应，确保返回统一的格式
        if (!isset($response['code'])) {
            return array(
                'code' => 0,
                'message' => 'success',
                'data' => $response
            );
        }
    }
    
    return $response;
}, 10, 3);

// 添加自定义API端点
add_action('rest_api_init', function() {
    // 健康检查接口
    register_rest_route('miniapp/v1', '/health-check', array(
        'methods' => 'GET',
        'callback' => function() {
            return array(
                'code' => 0,
                'message' => 'API is working',
                'data' => array(
                    'timestamp' => current_time('timestamp'),
                    'version' => '1.0.0'
                )
            );
        },
        'permission_callback' => '__return_true'
    ));
});

// 添加调试日志功能
if (!function_exists('write_api_log')) {
    function write_api_log($message, $type = 'info') {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_file = WP_CONTENT_DIR . '/debug-api.log';
            $time = current_time('mysql');
            $log_message = "[$time][$type] $message\n";
            file_put_contents($log_file, $log_message, FILE_APPEND);
        }
    }
}

// 监控API性能
add_action('rest_api_init', function() {
    if (!defined('REST_REQUEST') || !REST_REQUEST) {
        return;
    }
    
    $start_time = microtime(true);
    
    add_action('shutdown', function() use ($start_time) {
        $execution_time = microtime(true) - $start_time;
        write_api_log(sprintf(
            'API执行时间: %.4f秒, URI: %s',
            $execution_time,
            $_SERVER['REQUEST_URI']
        ), 'performance');
    });
});
// 重定向作者页面
function redirect_author_page() {
    if (is_author() && !current_user_can('edit_posts')) {
        wp_redirect(home_url());
        exit;
    }
}
add_action('template_redirect', 'redirect_author_page');
/**
 * 自定义函数
 * VIP群功能
 */

// 添加VIP群菜单
function zib_add_vip_wechat_menu() {
    add_menu_page(
        'VIP群设置',           // 页面标题
        'VIP群设置',           // 菜单标题
        'manage_options',      // 权限
        'vip-wechat-settings', // 菜单slug
        'zib_vip_wechat_settings_page', // 回调函数
        'dashicons-groups',    // 图标
        100                    // 位置
    );
}
add_action('admin_menu', 'zib_add_vip_wechat_menu');

// 注册设置
function zib_register_vip_wechat_settings() {
    register_setting('zib-vip-wechat-settings-group', 'zib_vip_wechat_qrcode');
    register_setting('zib-vip-wechat-settings-group', 'zib_vip_wechat_notice');
}
add_action('admin_init', 'zib_register_vip_wechat_settings');

// 添加媒体上传脚本
function zib_vip_wechat_admin_scripts() {
    if (isset($_GET['page']) && $_GET['page'] === 'vip-wechat-settings') {
        wp_enqueue_media();
        add_action('admin_footer', 'zib_vip_wechat_admin_scripts_js');
    }
}
add_action('admin_enqueue_scripts', 'zib_vip_wechat_admin_scripts');

function zib_vip_wechat_admin_scripts_js() {
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#upload_qrcode_button').click(function(e) {
            e.preventDefault();
            var image = wp.media({ 
                title: '选择二维码图片',
                multiple: false
            }).open()
            .on('select', function(e){
                var uploaded_image = image.state().get('selection').first();
                var image_url = uploaded_image.toJSON().url;
                $('#qrcode_url').val(image_url);
                $('#qrcode_preview').attr('src', image_url).show();
            });
        });
    });
    </script>
    <?php
}
// 设置页面
function zib_vip_wechat_settings_page() {
    // 检查权限
    if (!current_user_can('manage_options')) {
        wp_die('您没有足够的权限访问此页面');
        return;
    }
    
    $message = '';
    
    // 处理表单提交
    if (isset($_POST['submit'])) {
        // 更新设置
        $qrcode = isset($_POST['zib_vip_wechat_qrcode']) ? sanitize_text_field($_POST['zib_vip_wechat_qrcode']) : '';
        $notice = isset($_POST['zib_vip_wechat_notice']) ? wp_kses_post($_POST['zib_vip_wechat_notice']) : '';
        
        update_option('zib_vip_wechat_qrcode', $qrcode);
        update_option('zib_vip_wechat_notice', $notice);
        
        // 直接显示成功消息
        $message = '<div class="updated"><p>设置已保存！</p></div>';
    }
    
    // 获取当前设置
    $qrcode_url = get_option('zib_vip_wechat_qrcode');
    $notice = get_option('zib_vip_wechat_notice');
    
    // 显示设置页面
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        
        <?php 
        // 显示保存消息
        if ($message) {
            echo $message;
        }
        ?>

        <form method="post" action="">
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">群二维码图片</th>
                    <td>
                        <div class="qrcode-upload-box">
                            <input id="qrcode_url" type="text" name="zib_vip_wechat_qrcode" style="width: 500px;" value="<?php echo esc_attr($qrcode_url); ?>" />
                            <button type="button" id="upload_qrcode_button" class="button">上传图片</button>
                            <p class="description">点击"上传图片"按钮选择或上传群二维码图片</p>
                            <div style="margin-top: 10px;">
                                <?php if ($qrcode_url) { ?>
                                    <img id="qrcode_preview" src="<?php echo esc_url($qrcode_url); ?>" style="max-width: 200px;" />
                                <?php } else { ?>
                                    <img id="qrcode_preview" src="" style="max-width: 200px; display: none;" />
                                <?php } ?>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr valign="top">
                    <th scope="row">群公告</th>
                    <td>
                        <textarea name="zib_vip_wechat_notice" rows="5" style="width: 500px;"><?php echo esc_textarea($notice); ?></textarea>
                        <p class="description">输入群公告内容，支持HTML代码</p>
                    </td>
                </tr>
            </table>
            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="保存更改">
            </p>
        </form>
    </div>
    <style>
    .qrcode-upload-box {
        margin-bottom: 10px;
    }
    #upload_qrcode_button {
        margin-left: 10px;
    }
    #qrcode_preview {
        border: 1px solid #ddd;
        padding: 5px;
        border-radius: 4px;
    }
    </style>
    
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#upload_qrcode_button').click(function(e) {
            e.preventDefault();
            var image = wp.media({ 
                title: '选择二维码图片',
                multiple: false
            }).open()
            .on('select', function(e){
                var uploaded_image = image.state().get('selection').first();
                var image_url = uploaded_image.toJSON().url;
                $('#qrcode_url').val(image_url);
                $('#qrcode_preview').attr('src', image_url).show();
            });
        });
    });
    </script>
    <?php
}

// 添加VIP群标签页到用户中心
function zib_add_vip_wechat_qrcode_tab($tabs) {
    $user_id = get_current_user_id();
    if ($user_id && function_exists('zib_get_user_vip_level') && zib_get_user_vip_level($user_id)) {
        $tabs['vip_wechat'] = array(
            'title' => 'VIP交流群',
            'nav_attr' => 'drawer-title="VIP交流群"',
            'content' => zib_get_vip_wechat_qrcode_content(),
            'loader' => '<div class="zib-widget"><div class="text-center"><p class="placeholder k1"></p><p class="placeholder t1"></p></div></div>'
        );
    }
    return $tabs;
}
add_filter('user_center_tabs', 'zib_add_vip_wechat_qrcode_tab');

// 获取VIP群内容
function zib_get_vip_wechat_qrcode_content() {
    $user_id = get_current_user_id();
    if (!$user_id || !zib_get_user_vip_level($user_id)) {
        return '<div class="text-center c-red padding-h10">
                    <div class="mb20"><i class="fa fa-info-circle mr10"></i>您还不是VIP会员，无法查看群二维码</div>
                    <div><a href="javascript:;" class="but c-red padding-lg pay-vip"><i class="fa fa-diamond mr10"></i>立即开通VIP</a></div>
                </div>';
    }

    $qrcode_url = get_option('zib_vip_wechat_qrcode');
    $notice = get_option('zib_vip_wechat_notice');

    $html = '<div class="text-center">';
    
    if ($qrcode_url) {
        $html .= '<div class="mb20">
                    <img src="' . esc_url($qrcode_url) . '" style="max-width: 200px;" alt="VIP群二维码">
                 </div>';
    }
    
    if ($notice) {
        $html .= '<div class="muted-box padding-10">
                    <div class="text-left">
                        <div class="mb6"><b>群公告：</b></div>
                        <div class="muted-2-color em09">' . wp_kses_post($notice) . '</div>
                    </div>
                 </div>';
    }
    
    $html .= '</div>';
    
    return $html;
}
// 引入VIP群功能优化文件
if (file_exists(get_theme_file_path('/inc/functions/zib-vip-wechat-optimize.php'))) {
    require_once get_theme_file_path('/inc/functions/zib-vip-wechat-optimize.php');
}
/**
 * 域名重定向功能 - www.chatgptcnnet.top 重定向到搜索页面
 * 使用HTTPS安全协议
 */
function custom_domain_redirect() {
    // 获取当前主机名
    $host = $_SERVER['HTTP_HOST'];
    
    // 获取当前请求的URI
    $request_uri = $_SERVER['REQUEST_URI'];
    
    // 检查是否是www.chatgptcnnet.top域名访问根目录
    if ($host === 'www.chatgptcnnet.top' && ($request_uri === '/' || $request_uri === '')) {
        // 使用HTTPS安全协议
        $redirect_url = 'https://zk.chatgptcnnet.top/%e7%ab%99%e5%86%85%e6%90%9c%e7%b4%a2-2/';
        
        // 使用301永久重定向，有利于SEO
        header('Location: ' . $redirect_url, true, 301);
        exit;
    }
}
// 完全禁用下载功能 - 拦截所有/pay-download/路径的访问
add_action('template_redirect', function() {
    if (preg_match('/\/pay-download\//i', $_SERVER['REQUEST_URI'])) {
        // 跳转到首页
        wp_redirect(home_url());
        exit;
    }
});
// 禁用作者页面重定向到首页
add_action('template_redirect', function() {
    if (is_author()) {
        wp_redirect(home_url());
        exit;
    }
});
// 使用WordPress的最早钩子，确保在任何其他代码执行前处理重定向
add_action('init', 'custom_domain_redirect', 1);